package main

import (
	"errors"
	"fmt"
	"math"
	"strings"
)

/*
Go语言进阶特性 - 测试编写（单元测试、基准测试）

本文件包含被测试的代码，对应的测试文件是 07_testing_test.go

涵盖内容：
1. 单元测试的编写
2. 表格驱动测试
3. 基准测试
4. 示例测试
5. 测试覆盖率
6. 模拟和依赖注入
*/

func main() {
	fmt.Println("=== Go语言测试学习 ===")
	fmt.Println("这个文件包含被测试的代码")
	fmt.Println("运行测试命令:")
	fmt.Println("  go test advanced/07_testing_test.go advanced/07_testing.go -v")
	fmt.Println("  go test -bench=. advanced/07_testing_test.go advanced/07_testing.go")
	fmt.Println("  go test -cover advanced/07_testing_test.go advanced/07_testing.go")
	
	// 演示一些函数的使用
	demonstrateUsage()
}

func demonstrateUsage() {
	fmt.Println("\n--- 函数使用演示 ---")
	
	// 数学函数
	fmt.Printf("Add(2, 3) = %d\n", Add(2, 3))
	fmt.Printf("Subtract(5, 3) = %d\n", Subtract(5, 3))
	fmt.Printf("Multiply(4, 5) = %d\n", Multiply(4, 5))
	
	result, err := Divide(10, 2)
	if err != nil {
		fmt.Printf("Divide error: %v\n", err)
	} else {
		fmt.Printf("Divide(10, 2) = %.2f\n", result)
	}
	
	// 字符串函数
	fmt.Printf("IsPalindrome(\"racecar\") = %t\n", IsPalindrome("racecar"))
	fmt.Printf("WordCount(\"hello world\") = %d\n", WordCount("hello world"))
	fmt.Printf("Reverse(\"hello\") = %s\n", Reverse("hello"))
	
	// 切片函数
	numbers := []int{3, 1, 4, 1, 5, 9}
	fmt.Printf("Max(%v) = %d\n", numbers, Max(numbers))
	fmt.Printf("Sum(%v) = %d\n", numbers, Sum(numbers))
	fmt.Printf("Contains(%v, 4) = %t\n", numbers, Contains(numbers, 4))
	
	// 用户管理
	userManager := NewUserManager()
	user := &User{ID: 1, Name: "张三", Email: "<EMAIL>", Age: 25}
	userManager.AddUser(user)
	
	foundUser, exists := userManager.GetUser(1)
	if exists {
		fmt.Printf("找到用户: %+v\n", foundUser)
	}
	
	// 计算器
	calc := &Calculator{}
	fmt.Printf("Calculator Add(10, 5) = %d\n", calc.Add(10, 5))
	fmt.Printf("Calculator Divide(10, 2) = %.2f\n", calc.Divide(10, 2))
}

// ============ 基本数学函数 ============

// Add 加法函数
func Add(a, b int) int {
	return a + b
}

// Subtract 减法函数
func Subtract(a, b int) int {
	return a - b
}

// Multiply 乘法函数
func Multiply(a, b int) int {
	return a * b
}

// Divide 除法函数，返回结果和错误
func Divide(a, b float64) (float64, error) {
	if b == 0 {
		return 0, errors.New("division by zero")
	}
	return a / b, nil
}

// Factorial 阶乘函数
func Factorial(n int) int {
	if n < 0 {
		return 0
	}
	if n <= 1 {
		return 1
	}
	return n * Factorial(n-1)
}

// IsPrime 判断是否为质数
func IsPrime(n int) bool {
	if n < 2 {
		return false
	}
	if n == 2 {
		return true
	}
	if n%2 == 0 {
		return false
	}
	
	sqrt := int(math.Sqrt(float64(n)))
	for i := 3; i <= sqrt; i += 2 {
		if n%i == 0 {
			return false
		}
	}
	return true
}

// ============ 字符串处理函数 ============

// IsPalindrome 判断是否为回文
func IsPalindrome(s string) bool {
	s = strings.ToLower(s)
	runes := []rune(s)
	length := len(runes)
	
	for i := 0; i < length/2; i++ {
		if runes[i] != runes[length-1-i] {
			return false
		}
	}
	return true
}

// WordCount 统计单词数量
func WordCount(s string) int {
	if s == "" {
		return 0
	}
	
	words := strings.Fields(s)
	return len(words)
}

// Reverse 反转字符串
func Reverse(s string) string {
	runes := []rune(s)
	length := len(runes)
	
	for i := 0; i < length/2; i++ {
		runes[i], runes[length-1-i] = runes[length-1-i], runes[i]
	}
	
	return string(runes)
}

// CapitalizeWords 将每个单词的首字母大写
func CapitalizeWords(s string) string {
	words := strings.Fields(s)
	for i, word := range words {
		if len(word) > 0 {
			words[i] = strings.ToUpper(string(word[0])) + strings.ToLower(word[1:])
		}
	}
	return strings.Join(words, " ")
}

// ============ 切片操作函数 ============

// Max 找到切片中的最大值
func Max(numbers []int) int {
	if len(numbers) == 0 {
		return 0
	}
	
	max := numbers[0]
	for _, num := range numbers[1:] {
		if num > max {
			max = num
		}
	}
	return max
}

// Min 找到切片中的最小值
func Min(numbers []int) int {
	if len(numbers) == 0 {
		return 0
	}
	
	min := numbers[0]
	for _, num := range numbers[1:] {
		if num < min {
			min = num
		}
	}
	return min
}

// Sum 计算切片元素的和
func Sum(numbers []int) int {
	sum := 0
	for _, num := range numbers {
		sum += num
	}
	return sum
}

// Average 计算平均值
func Average(numbers []int) float64 {
	if len(numbers) == 0 {
		return 0
	}
	return float64(Sum(numbers)) / float64(len(numbers))
}

// Contains 检查切片是否包含指定元素
func Contains(numbers []int, target int) bool {
	for _, num := range numbers {
		if num == target {
			return true
		}
	}
	return false
}

// Remove 从切片中移除指定元素
func Remove(numbers []int, target int) []int {
	var result []int
	for _, num := range numbers {
		if num != target {
			result = append(result, num)
		}
	}
	return result
}

// ============ 用户管理系统 ============

// User 用户结构体
type User struct {
	ID    int    `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
	Age   int    `json:"age"`
}

// Validate 验证用户数据
func (u *User) Validate() error {
	if u.Name == "" {
		return errors.New("name cannot be empty")
	}
	if u.Email == "" {
		return errors.New("email cannot be empty")
	}
	if u.Age < 0 || u.Age > 150 {
		return errors.New("age must be between 0 and 150")
	}
	if !strings.Contains(u.Email, "@") {
		return errors.New("invalid email format")
	}
	return nil
}

// IsAdult 判断是否成年
func (u *User) IsAdult() bool {
	return u.Age >= 18
}

// UserManager 用户管理器
type UserManager struct {
	users map[int]*User
}

// NewUserManager 创建新的用户管理器
func NewUserManager() *UserManager {
	return &UserManager{
		users: make(map[int]*User),
	}
}

// AddUser 添加用户
func (um *UserManager) AddUser(user *User) error {
	if err := user.Validate(); err != nil {
		return err
	}
	
	if _, exists := um.users[user.ID]; exists {
		return errors.New("user already exists")
	}
	
	um.users[user.ID] = user
	return nil
}

// GetUser 获取用户
func (um *UserManager) GetUser(id int) (*User, bool) {
	user, exists := um.users[id]
	return user, exists
}

// UpdateUser 更新用户
func (um *UserManager) UpdateUser(user *User) error {
	if err := user.Validate(); err != nil {
		return err
	}
	
	if _, exists := um.users[user.ID]; !exists {
		return errors.New("user not found")
	}
	
	um.users[user.ID] = user
	return nil
}

// DeleteUser 删除用户
func (um *UserManager) DeleteUser(id int) error {
	if _, exists := um.users[id]; !exists {
		return errors.New("user not found")
	}
	
	delete(um.users, id)
	return nil
}

// GetAllUsers 获取所有用户
func (um *UserManager) GetAllUsers() []*User {
	var users []*User
	for _, user := range um.users {
		users = append(users, user)
	}
	return users
}

// GetUserCount 获取用户数量
func (um *UserManager) GetUserCount() int {
	return len(um.users)
}

// ============ 计算器 ============

// Calculator 计算器结构体
type Calculator struct {
	history []string
}

// Add 加法
func (c *Calculator) Add(a, b int) int {
	result := a + b
	c.addToHistory(fmt.Sprintf("%d + %d = %d", a, b, result))
	return result
}

// Subtract 减法
func (c *Calculator) Subtract(a, b int) int {
	result := a - b
	c.addToHistory(fmt.Sprintf("%d - %d = %d", a, b, result))
	return result
}

// Multiply 乘法
func (c *Calculator) Multiply(a, b int) int {
	result := a * b
	c.addToHistory(fmt.Sprintf("%d * %d = %d", a, b, result))
	return result
}

// Divide 除法
func (c *Calculator) Divide(a, b float64) float64 {
	if b == 0 {
		c.addToHistory(fmt.Sprintf("%.2f / %.2f = ERROR (division by zero)", a, b))
		return 0
	}
	result := a / b
	c.addToHistory(fmt.Sprintf("%.2f / %.2f = %.2f", a, b, result))
	return result
}

// Power 幂运算
func (c *Calculator) Power(base, exponent int) int {
	result := 1
	for i := 0; i < exponent; i++ {
		result *= base
	}
	c.addToHistory(fmt.Sprintf("%d ^ %d = %d", base, exponent, result))
	return result
}

// GetHistory 获取计算历史
func (c *Calculator) GetHistory() []string {
	return c.history
}

// ClearHistory 清空历史
func (c *Calculator) ClearHistory() {
	c.history = nil
}

// addToHistory 添加到历史记录
func (c *Calculator) addToHistory(operation string) {
	c.history = append(c.history, operation)
}

// ============ 排序算法 ============

// BubbleSort 冒泡排序
func BubbleSort(arr []int) []int {
	result := make([]int, len(arr))
	copy(result, arr)
	
	n := len(result)
	for i := 0; i < n-1; i++ {
		for j := 0; j < n-i-1; j++ {
			if result[j] > result[j+1] {
				result[j], result[j+1] = result[j+1], result[j]
			}
		}
	}
	return result
}

// QuickSort 快速排序
func QuickSort(arr []int) []int {
	result := make([]int, len(arr))
	copy(result, arr)
	quickSortHelper(result, 0, len(result)-1)
	return result
}

func quickSortHelper(arr []int, low, high int) {
	if low < high {
		pi := partition(arr, low, high)
		quickSortHelper(arr, low, pi-1)
		quickSortHelper(arr, pi+1, high)
	}
}

func partition(arr []int, low, high int) int {
	pivot := arr[high]
	i := low - 1
	
	for j := low; j < high; j++ {
		if arr[j] < pivot {
			i++
			arr[i], arr[j] = arr[j], arr[i]
		}
	}
	arr[i+1], arr[high] = arr[high], arr[i+1]
	return i + 1
}

// ============ 数据结构 ============

// Stack 栈结构
type Stack struct {
	items []int
}

// NewStack 创建新栈
func NewStack() *Stack {
	return &Stack{items: make([]int, 0)}
}

// Push 入栈
func (s *Stack) Push(item int) {
	s.items = append(s.items, item)
}

// Pop 出栈
func (s *Stack) Pop() (int, error) {
	if len(s.items) == 0 {
		return 0, errors.New("stack is empty")
	}
	
	index := len(s.items) - 1
	item := s.items[index]
	s.items = s.items[:index]
	return item, nil
}

// Peek 查看栈顶元素
func (s *Stack) Peek() (int, error) {
	if len(s.items) == 0 {
		return 0, errors.New("stack is empty")
	}
	return s.items[len(s.items)-1], nil
}

// Size 获取栈大小
func (s *Stack) Size() int {
	return len(s.items)
}

// IsEmpty 检查栈是否为空
func (s *Stack) IsEmpty() bool {
	return len(s.items) == 0
}

/*
这个文件包含了各种类型的函数，用于演示Go语言的测试编写：

1. 基本数学函数 - 用于演示简单的单元测试
2. 字符串处理函数 - 用于演示表格驱动测试
3. 切片操作函数 - 用于演示边界条件测试
4. 用户管理系统 - 用于演示复杂对象的测试
5. 计算器 - 用于演示状态管理的测试
6. 排序算法 - 用于演示基准测试
7. 数据结构 - 用于演示错误处理测试

对应的测试文件是 07_testing_test.go
*/
