package main

import (
	"context"
	"fmt"
	"math/rand"
	"runtime"
	"sync"
	"time"
)

/*
Go语言进阶特性 - 并发编程（goroutine、channel）

本文件涵盖：
1. goroutine的创建和使用
2. channel的基本操作
3. 有缓冲和无缓冲channel
4. channel的方向性
5. select语句
6. 同步原语（sync包）
7. 并发模式和最佳实践
*/

func main() {
	fmt.Println("=== Go语言并发编程学习 ===")
	
	// 1. goroutine基础
	demonstrateGoroutines()
	
	// 2. channel基础
	demonstrateChannels()
	
	// 3. 有缓冲channel
	demonstrateBufferedChannels()
	
	// 4. select语句
	demonstrateSelect()
	
	// 5. 同步原语
	demonstrateSyncPrimitives()
	
	// 6. 并发模式
	demonstrateConcurrencyPatterns()
	
	// 7. 实用示例
	demonstratePracticalExamples()
}

// 演示goroutine基础
func demonstrateGoroutines() {
	fmt.Println("\n--- goroutine基础 ---")
	
	// 1. 基本goroutine
	fmt.Println("1. 基本goroutine:")
	go sayHello("World")
	go sayHello("Go")
	go sayHello("Goroutine")
	
	// 等待goroutine执行完成
	time.Sleep(100 * time.Millisecond)
	
	// 2. 匿名函数goroutine
	fmt.Println("\n2. 匿名函数goroutine:")
	for i := 0; i < 3; i++ {
		go func(id int) {
			fmt.Printf("  匿名goroutine %d 执行\n", id)
		}(i) // 传递参数避免闭包陷阱
	}
	
	time.Sleep(100 * time.Millisecond)
	
	// 3. 闭包陷阱示例
	fmt.Println("\n3. 闭包陷阱:")
	fmt.Println("错误的方式:")
	for i := 0; i < 3; i++ {
		go func() {
			fmt.Printf("  错误: goroutine %d\n", i) // 可能都打印3
		}()
	}
	time.Sleep(50 * time.Millisecond)
	
	fmt.Println("正确的方式:")
	for i := 0; i < 3; i++ {
		go func(id int) {
			fmt.Printf("  正确: goroutine %d\n", id)
		}(i)
	}
	time.Sleep(50 * time.Millisecond)
	
	// 4. 获取goroutine数量
	fmt.Printf("\n当前goroutine数量: %d\n", runtime.NumGoroutine())
}

func sayHello(name string) {
	fmt.Printf("  Hello, %s!\n", name)
}

// 演示channel基础
func demonstrateChannels() {
	fmt.Println("\n--- channel基础 ---")
	
	// 1. 无缓冲channel
	fmt.Println("1. 无缓冲channel:")
	ch := make(chan string)
	
	go func() {
		ch <- "Hello from goroutine"
	}()
	
	message := <-ch
	fmt.Printf("  接收到消息: %s\n", message)
	
	// 2. channel的关闭
	fmt.Println("\n2. channel关闭:")
	ch2 := make(chan int)
	
	go func() {
		for i := 0; i < 3; i++ {
			ch2 <- i
		}
		close(ch2) // 关闭channel
	}()
	
	// 使用range接收直到channel关闭
	for value := range ch2 {
		fmt.Printf("  接收到: %d\n", value)
	}
	
	// 3. 检查channel是否关闭
	fmt.Println("\n3. 检查channel状态:")
	ch3 := make(chan int, 1)
	ch3 <- 42
	close(ch3)
	
	value, ok := <-ch3
	fmt.Printf("  第一次接收: value=%d, ok=%t\n", value, ok)
	
	value, ok = <-ch3
	fmt.Printf("  第二次接收: value=%d, ok=%t\n", value, ok)
	
	// 4. 只读和只写channel
	fmt.Println("\n4. channel方向性:")
	ch4 := make(chan string)
	
	go sender(ch4)
	go receiver(ch4)
	
	time.Sleep(100 * time.Millisecond)
}

// 只写channel参数
func sender(ch chan<- string) {
	ch <- "消息1"
	ch <- "消息2"
	close(ch)
}

// 只读channel参数
func receiver(ch <-chan string) {
	for msg := range ch {
		fmt.Printf("  接收器收到: %s\n", msg)
	}
}

// 演示有缓冲channel
func demonstrateBufferedChannels() {
	fmt.Println("\n--- 有缓冲channel ---")
	
	// 1. 基本有缓冲channel
	fmt.Println("1. 基本有缓冲channel:")
	ch := make(chan int, 3) // 缓冲区大小为3
	
	// 可以发送3个值而不阻塞
	ch <- 1
	ch <- 2
	ch <- 3
	
	fmt.Printf("  channel长度: %d, 容量: %d\n", len(ch), cap(ch))
	
	// 接收值
	for i := 0; i < 3; i++ {
		value := <-ch
		fmt.Printf("  接收到: %d\n", value)
	}
	
	// 2. 生产者-消费者模式
	fmt.Println("\n2. 生产者-消费者模式:")
	buffer := make(chan int, 5)
	
	// 生产者
	go func() {
		for i := 0; i < 10; i++ {
			buffer <- i
			fmt.Printf("  生产: %d\n", i)
			time.Sleep(50 * time.Millisecond)
		}
		close(buffer)
	}()
	
	// 消费者
	go func() {
		for value := range buffer {
			fmt.Printf("  消费: %d\n", value)
			time.Sleep(100 * time.Millisecond) // 消费比生产慢
		}
	}()
	
	time.Sleep(2 * time.Second)
	
	// 3. 工作池模式
	fmt.Println("\n3. 工作池模式:")
	jobs := make(chan int, 10)
	results := make(chan int, 10)
	
	// 启动3个工作者
	for w := 1; w <= 3; w++ {
		go worker(w, jobs, results)
	}
	
	// 发送任务
	for j := 1; j <= 5; j++ {
		jobs <- j
	}
	close(jobs)
	
	// 收集结果
	for r := 1; r <= 5; r++ {
		result := <-results
		fmt.Printf("  结果: %d\n", result)
	}
}

func worker(id int, jobs <-chan int, results chan<- int) {
	for job := range jobs {
		fmt.Printf("  工作者 %d 开始任务 %d\n", id, job)
		time.Sleep(time.Duration(rand.Intn(100)) * time.Millisecond)
		results <- job * 2
		fmt.Printf("  工作者 %d 完成任务 %d\n", id, job)
	}
}

// 演示select语句
func demonstrateSelect() {
	fmt.Println("\n--- select语句 ---")
	
	// 1. 基本select
	fmt.Println("1. 基本select:")
	ch1 := make(chan string)
	ch2 := make(chan string)
	
	go func() {
		time.Sleep(100 * time.Millisecond)
		ch1 <- "来自ch1的消息"
	}()
	
	go func() {
		time.Sleep(200 * time.Millisecond)
		ch2 <- "来自ch2的消息"
	}()
	
	for i := 0; i < 2; i++ {
		select {
		case msg1 := <-ch1:
			fmt.Printf("  接收到ch1: %s\n", msg1)
		case msg2 := <-ch2:
			fmt.Printf("  接收到ch2: %s\n", msg2)
		}
	}
	
	// 2. 带default的select
	fmt.Println("\n2. 带default的select:")
	ch3 := make(chan string)
	
	select {
	case msg := <-ch3:
		fmt.Printf("  接收到消息: %s\n", msg)
	default:
		fmt.Println("  没有消息可接收")
	}
	
	// 3. 超时模式
	fmt.Println("\n3. 超时模式:")
	ch4 := make(chan string)
	
	go func() {
		time.Sleep(2 * time.Second)
		ch4 <- "延迟消息"
	}()
	
	select {
	case msg := <-ch4:
		fmt.Printf("  接收到: %s\n", msg)
	case <-time.After(1 * time.Second):
		fmt.Println("  操作超时")
	}
	
	// 4. 定时器模式
	fmt.Println("\n4. 定时器模式:")
	ticker := time.NewTicker(200 * time.Millisecond)
	defer ticker.Stop()
	
	done := make(chan bool)
	go func() {
		time.Sleep(1 * time.Second)
		done <- true
	}()
	
	for {
		select {
		case <-ticker.C:
			fmt.Println("  定时器触发")
		case <-done:
			fmt.Println("  定时器结束")
			return
		}
	}
}

// 演示同步原语
func demonstrateSyncPrimitives() {
	fmt.Println("\n--- 同步原语 ---")
	
	// 1. WaitGroup
	fmt.Println("1. WaitGroup:")
	var wg sync.WaitGroup
	
	for i := 1; i <= 3; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			fmt.Printf("  goroutine %d 执行\n", id)
			time.Sleep(100 * time.Millisecond)
		}(i)
	}
	
	wg.Wait()
	fmt.Println("  所有goroutine完成")
	
	// 2. Mutex
	fmt.Println("\n2. Mutex:")
	var mutex sync.Mutex
	var counter int
	
	var wg2 sync.WaitGroup
	for i := 0; i < 5; i++ {
		wg2.Add(1)
		go func(id int) {
			defer wg2.Done()
			for j := 0; j < 100; j++ {
				mutex.Lock()
				counter++
				mutex.Unlock()
			}
			fmt.Printf("  goroutine %d 完成\n", id)
		}(i)
	}
	
	wg2.Wait()
	fmt.Printf("  最终计数: %d\n", counter)
	
	// 3. RWMutex
	fmt.Println("\n3. RWMutex:")
	var rwMutex sync.RWMutex
	var data = make(map[string]int)
	
	// 写入数据
	go func() {
		for i := 0; i < 3; i++ {
			rwMutex.Lock()
			data[fmt.Sprintf("key%d", i)] = i
			fmt.Printf("  写入 key%d = %d\n", i, i)
			rwMutex.Unlock()
			time.Sleep(100 * time.Millisecond)
		}
	}()
	
	// 多个读取者
	for i := 0; i < 3; i++ {
		go func(id int) {
			time.Sleep(50 * time.Millisecond)
			rwMutex.RLock()
			fmt.Printf("  读取者 %d: %v\n", id, data)
			rwMutex.RUnlock()
		}(i)
	}
	
	time.Sleep(500 * time.Millisecond)
	
	// 4. Once
	fmt.Println("\n4. Once:")
	var once sync.Once
	
	for i := 0; i < 3; i++ {
		go func(id int) {
			once.Do(func() {
				fmt.Printf("  只执行一次的初始化 (goroutine %d)\n", id)
			})
		}(i)
	}
	
	time.Sleep(100 * time.Millisecond)
}

// 演示并发模式
func demonstrateConcurrencyPatterns() {
	fmt.Println("\n--- 并发模式 ---")
	
	// 1. 扇出/扇入模式
	fmt.Println("1. 扇出/扇入模式:")
	input := make(chan int)
	
	// 扇出：一个输入分发给多个处理器
	output1 := processor("处理器1", input)
	output2 := processor("处理器2", input)
	output3 := processor("处理器3", input)
	
	// 扇入：多个输出合并到一个channel
	merged := merge(output1, output2, output3)
	
	// 发送数据
	go func() {
		for i := 1; i <= 6; i++ {
			input <- i
		}
		close(input)
	}()
	
	// 接收合并后的结果
	for result := range merged {
		fmt.Printf("  合并结果: %s\n", result)
	}
	
	// 2. 管道模式
	fmt.Println("\n2. 管道模式:")
	numbers := generator(1, 2, 3, 4, 5)
	squared := square(numbers)
	doubled := double(squared)
	
	for result := range doubled {
		fmt.Printf("  管道结果: %d\n", result)
	}
	
	// 3. 取消模式
	fmt.Println("\n3. 取消模式:")
	ctx, cancel := context.WithCancel(context.Background())
	
	go func() {
		time.Sleep(500 * time.Millisecond)
		cancel() // 取消操作
	}()
	
	cancellableWork(ctx)
}

// 处理器函数
func processor(name string, input <-chan int) <-chan string {
	output := make(chan string)
	go func() {
		defer close(output)
		for value := range input {
			result := fmt.Sprintf("%s处理了%d", name, value)
			output <- result
			time.Sleep(100 * time.Millisecond)
		}
	}()
	return output
}

// 合并多个channel
func merge(channels ...<-chan string) <-chan string {
	output := make(chan string)
	var wg sync.WaitGroup
	
	for _, ch := range channels {
		wg.Add(1)
		go func(c <-chan string) {
			defer wg.Done()
			for value := range c {
				output <- value
			}
		}(ch)
	}
	
	go func() {
		wg.Wait()
		close(output)
	}()
	
	return output
}

// 生成器
func generator(numbers ...int) <-chan int {
	output := make(chan int)
	go func() {
		defer close(output)
		for _, num := range numbers {
			output <- num
		}
	}()
	return output
}

// 平方处理
func square(input <-chan int) <-chan int {
	output := make(chan int)
	go func() {
		defer close(output)
		for num := range input {
			output <- num * num
		}
	}()
	return output
}

// 翻倍处理
func double(input <-chan int) <-chan int {
	output := make(chan int)
	go func() {
		defer close(output)
		for num := range input {
			output <- num * 2
		}
	}()
	return output
}

// 可取消的工作
func cancellableWork(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			fmt.Println("  工作被取消")
			return
		default:
			fmt.Println("  正在工作...")
			time.Sleep(100 * time.Millisecond)
		}
	}
}

// 演示实用示例
func demonstratePracticalExamples() {
	fmt.Println("\n--- 实用示例 ---")
	
	// 1. 并发下载器
	fmt.Println("1. 并发下载器:")
	urls := []string{
		"https://example.com/file1",
		"https://example.com/file2",
		"https://example.com/file3",
		"https://example.com/file4",
	}
	
	results := concurrentDownload(urls)
	for result := range results {
		fmt.Printf("  %s\n", result)
	}
	
	// 2. 限流器
	fmt.Println("\n2. 限流器:")
	rateLimiter := time.NewTicker(200 * time.Millisecond)
	defer rateLimiter.Stop()
	
	requests := make(chan int, 5)
	for i := 1; i <= 5; i++ {
		requests <- i
	}
	close(requests)
	
	for req := range requests {
		<-rateLimiter.C // 等待限流器
		fmt.Printf("  处理请求 %d\n", req)
	}
	
	// 3. 超时重试
	fmt.Println("\n3. 超时重试:")
	if err := retryWithTimeout(3, 500*time.Millisecond); err != nil {
		fmt.Printf("  重试失败: %v\n", err)
	} else {
		fmt.Println("  操作成功")
	}
}

// 并发下载模拟
func concurrentDownload(urls []string) <-chan string {
	results := make(chan string)
	
	go func() {
		defer close(results)
		var wg sync.WaitGroup
		
		for _, url := range urls {
			wg.Add(1)
			go func(u string) {
				defer wg.Done()
				// 模拟下载
				time.Sleep(time.Duration(rand.Intn(300)) * time.Millisecond)
				results <- fmt.Sprintf("下载完成: %s", u)
			}(url)
		}
		
		wg.Wait()
	}()
	
	return results
}

// 超时重试
func retryWithTimeout(maxRetries int, timeout time.Duration) error {
	for i := 0; i < maxRetries; i++ {
		done := make(chan error, 1)
		
		go func() {
			// 模拟可能失败的操作
			time.Sleep(time.Duration(rand.Intn(1000)) * time.Millisecond)
			if rand.Float32() < 0.7 { // 70%失败率
				done <- fmt.Errorf("操作失败")
			} else {
				done <- nil
			}
		}()
		
		select {
		case err := <-done:
			if err == nil {
				return nil // 成功
			}
			fmt.Printf("  第%d次尝试失败: %v\n", i+1, err)
		case <-time.After(timeout):
			fmt.Printf("  第%d次尝试超时\n", i+1)
		}
	}
	
	return fmt.Errorf("重试%d次后仍然失败", maxRetries)
}

/*
运行此程序的命令：
go run advanced/03_concurrency.go

学习要点：
1. goroutine是Go的轻量级线程，使用go关键字启动
2. channel是goroutine之间通信的管道，分为有缓冲和无缓冲
3. select语句用于处理多个channel操作，支持非阻塞和超时
4. sync包提供了传统的同步原语：Mutex、RWMutex、WaitGroup、Once
5. 常见并发模式：扇出/扇入、管道、工作池、生产者-消费者
6. context包用于取消和超时控制
7. 并发编程要注意竞态条件、死锁和资源泄露
8. "不要通过共享内存来通信，而要通过通信来共享内存"
*/
