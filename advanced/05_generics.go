package main

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
)

/*
Go语言进阶特性 - 泛型编程

本文件涵盖：
1. 泛型函数的定义和使用
2. 类型约束和接口
3. 泛型类型的定义
4. 类型推断
5. 泛型的实用示例
6. 泛型的最佳实践

注意：泛型是Go 1.18+的特性
*/

func main() {
	fmt.Println("=== Go语言泛型编程学习 ===")
	
	// 1. 泛型函数基础
	demonstrateGenericFunctions()
	
	// 2. 类型约束
	demonstrateTypeConstraints()
	
	// 3. 泛型类型
	demonstrateGenericTypes()
	
	// 4. 类型推断
	demonstrateTypeInference()
	
	// 5. 实用示例
	demonstratePracticalExamples()
}

// 演示泛型函数基础
func demonstrateGenericFunctions() {
	fmt.Println("\n--- 泛型函数基础 ---")
	
	// 1. 基本泛型函数
	fmt.Println("1. 基本泛型函数:")
	
	// 整数
	intResult := Max(10, 20)
	fmt.Printf("Max(10, 20) = %d\n", intResult)
	
	// 浮点数
	floatResult := Max(3.14, 2.71)
	fmt.Printf("Max(3.14, 2.71) = %.2f\n", floatResult)
	
	// 字符串
	stringResult := Max("apple", "banana")
	fmt.Printf("Max(\"apple\", \"banana\") = %s\n", stringResult)
	
	// 2. 泛型切片操作
	fmt.Println("\n2. 泛型切片操作:")
	
	intSlice := []int{1, 2, 3, 4, 5}
	fmt.Printf("原始切片: %v\n", intSlice)
	
	reversedInt := Reverse(intSlice)
	fmt.Printf("反转后: %v\n", reversedInt)
	
	stringSlice := []string{"hello", "world", "go"}
	fmt.Printf("原始切片: %v\n", stringSlice)
	
	reversedString := Reverse(stringSlice)
	fmt.Printf("反转后: %v\n", reversedString)
	
	// 3. 泛型查找
	fmt.Println("\n3. 泛型查找:")
	
	numbers := []int{1, 3, 5, 7, 9}
	index := Find(numbers, 5)
	fmt.Printf("在 %v 中查找 5: 索引 %d\n", numbers, index)
	
	words := []string{"apple", "banana", "cherry"}
	index = Find(words, "banana")
	fmt.Printf("在 %v 中查找 \"banana\": 索引 %d\n", words, index)
	
	// 4. 泛型映射操作
	fmt.Println("\n4. 泛型映射操作:")
	
	numbers2 := []int{1, 2, 3, 4, 5}
	doubled := Map(numbers2, func(x int) int { return x * 2 })
	fmt.Printf("原始: %v, 翻倍: %v\n", numbers2, doubled)
	
	words2 := []string{"hello", "world"}
	lengths := Map(words2, func(s string) int { return len(s) })
	fmt.Printf("单词: %v, 长度: %v\n", words2, lengths)
}

// 泛型Max函数 - 使用Ordered约束
func Max[T Ordered](a, b T) T {
	if a > b {
		return a
	}
	return b
}

// 定义Ordered约束
type Ordered interface {
	~int | ~int8 | ~int16 | ~int32 | ~int64 |
		~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~uintptr |
		~float32 | ~float64 |
		~string
}

// 泛型反转函数
func Reverse[T any](slice []T) []T {
	result := make([]T, len(slice))
	for i, v := range slice {
		result[len(slice)-1-i] = v
	}
	return result
}

// 泛型查找函数
func Find[T comparable](slice []T, target T) int {
	for i, v := range slice {
		if v == target {
			return i
		}
	}
	return -1
}

// 泛型映射函数
func Map[T, U any](slice []T, fn func(T) U) []U {
	result := make([]U, len(slice))
	for i, v := range slice {
		result[i] = fn(v)
	}
	return result
}

// 演示类型约束
func demonstrateTypeConstraints() {
	fmt.Println("\n--- 类型约束 ---")
	
	// 1. 数值类型约束
	fmt.Println("1. 数值类型约束:")
	
	intSum := Sum([]int{1, 2, 3, 4, 5})
	fmt.Printf("整数求和: %d\n", intSum)
	
	floatSum := Sum([]float64{1.1, 2.2, 3.3})
	fmt.Printf("浮点数求和: %.1f\n", floatSum)
	
	// 2. 可比较类型约束
	fmt.Println("\n2. 可比较类型约束:")
	
	intSlice := []int{3, 1, 4, 1, 5, 9}
	uniqueInts := Unique(intSlice)
	fmt.Printf("原始: %v, 去重: %v\n", intSlice, uniqueInts)
	
	stringSlice := []string{"apple", "banana", "apple", "cherry"}
	uniqueStrings := Unique(stringSlice)
	fmt.Printf("原始: %v, 去重: %v\n", stringSlice, uniqueStrings)
	
	// 3. 自定义约束
	fmt.Println("\n3. 自定义约束:")
	
	fmt.Printf("字符串长度: %d\n", Length("hello"))
	fmt.Printf("切片长度: %d\n", Length([]int{1, 2, 3}))
	fmt.Printf("映射长度: %d\n", Length(map[string]int{"a": 1, "b": 2}))
	
	// 4. 方法约束
	fmt.Println("\n4. 方法约束:")
	
	p1 := Point{X: 1, Y: 2}
	p2 := Point{X: 4, Y: 6}
	fmt.Printf("点1: %s\n", p1.String())
	fmt.Printf("点2: %s\n", p2.String())
	fmt.Printf("距离: %.2f\n", Distance(p1, p2))
}

// 数值类型约束
type Numeric interface {
	~int | ~int8 | ~int16 | ~int32 | ~int64 |
		~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 |
		~float32 | ~float64
}

// 泛型求和函数
func Sum[T Numeric](slice []T) T {
	var sum T
	for _, v := range slice {
		sum += v
	}
	return sum
}

// 泛型去重函数
func Unique[T comparable](slice []T) []T {
	seen := make(map[T]bool)
	var result []T
	
	for _, v := range slice {
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}
	
	return result
}

// 长度约束
type Lengther interface {
	~string | ~[]any | ~map[any]any
}

// 泛型长度函数（简化版本）
func Length[T ~string](s T) int {
	return len(s)
}

// 重载版本用于切片
func LengthSlice[T any](slice []T) int {
	return len(slice)
}

// 重载版本用于映射
func LengthMap[K comparable, V any](m map[K]V) map[K]V {
	return m
}

// 定义带方法的约束
type Stringer interface {
	String() string
}

type Distancer interface {
	Distance(other Distancer) float64
}

// 点结构体
type Point struct {
	X, Y float64
}

func (p Point) String() string {
	return fmt.Sprintf("Point(%.1f, %.1f)", p.X, p.Y)
}

func (p Point) Distance(other Point) float64 {
	dx := p.X - other.X
	dy := p.Y - other.Y
	return (dx*dx + dy*dy) // 简化版本，不开平方根
}

// 泛型距离函数
func Distance[T interface{ Distance(T) float64 }](a, b T) float64 {
	return a.Distance(b)
}

// 演示泛型类型
func demonstrateGenericTypes() {
	fmt.Println("\n--- 泛型类型 ---")
	
	// 1. 泛型栈
	fmt.Println("1. 泛型栈:")
	
	intStack := NewStack[int]()
	intStack.Push(1)
	intStack.Push(2)
	intStack.Push(3)
	
	fmt.Printf("栈大小: %d\n", intStack.Size())
	fmt.Printf("栈顶: %d\n", intStack.Peek())
	fmt.Printf("弹出: %d\n", intStack.Pop())
	fmt.Printf("弹出后栈大小: %d\n", intStack.Size())
	
	stringStack := NewStack[string]()
	stringStack.Push("hello")
	stringStack.Push("world")
	
	fmt.Printf("字符串栈顶: %s\n", stringStack.Peek())
	
	// 2. 泛型队列
	fmt.Println("\n2. 泛型队列:")
	
	queue := NewQueue[string]()
	queue.Enqueue("first")
	queue.Enqueue("second")
	queue.Enqueue("third")
	
	fmt.Printf("队列大小: %d\n", queue.Size())
	fmt.Printf("队首: %s\n", queue.Front())
	fmt.Printf("出队: %s\n", queue.Dequeue())
	fmt.Printf("出队后队首: %s\n", queue.Front())
	
	// 3. 泛型映射
	fmt.Println("\n3. 泛型安全映射:")
	
	safeMap := NewSafeMap[string, int]()
	safeMap.Set("apple", 5)
	safeMap.Set("banana", 3)
	
	if value, ok := safeMap.Get("apple"); ok {
		fmt.Printf("apple: %d\n", value)
	}
	
	fmt.Printf("映射大小: %d\n", safeMap.Size())
	fmt.Printf("所有键: %v\n", safeMap.Keys())
	
	// 4. 泛型结果类型
	fmt.Println("\n4. 泛型结果类型:")
	
	result1 := Divide(10, 2)
	if result1.IsOk() {
		fmt.Printf("10 / 2 = %.2f\n", result1.Unwrap())
	}
	
	result2 := Divide(10, 0)
	if result2.IsErr() {
		fmt.Printf("10 / 0 错误: %s\n", result2.Error())
	}
}

// 泛型栈
type Stack[T any] struct {
	items []T
}

func NewStack[T any]() *Stack[T] {
	return &Stack[T]{items: make([]T, 0)}
}

func (s *Stack[T]) Push(item T) {
	s.items = append(s.items, item)
}

func (s *Stack[T]) Pop() T {
	if len(s.items) == 0 {
		var zero T
		return zero
	}
	index := len(s.items) - 1
	item := s.items[index]
	s.items = s.items[:index]
	return item
}

func (s *Stack[T]) Peek() T {
	if len(s.items) == 0 {
		var zero T
		return zero
	}
	return s.items[len(s.items)-1]
}

func (s *Stack[T]) Size() int {
	return len(s.items)
}

func (s *Stack[T]) IsEmpty() bool {
	return len(s.items) == 0
}

// 泛型队列
type Queue[T any] struct {
	items []T
}

func NewQueue[T any]() *Queue[T] {
	return &Queue[T]{items: make([]T, 0)}
}

func (q *Queue[T]) Enqueue(item T) {
	q.items = append(q.items, item)
}

func (q *Queue[T]) Dequeue() T {
	if len(q.items) == 0 {
		var zero T
		return zero
	}
	item := q.items[0]
	q.items = q.items[1:]
	return item
}

func (q *Queue[T]) Front() T {
	if len(q.items) == 0 {
		var zero T
		return zero
	}
	return q.items[0]
}

func (q *Queue[T]) Size() int {
	return len(q.items)
}

func (q *Queue[T]) IsEmpty() bool {
	return len(q.items) == 0
}

// 泛型安全映射
type SafeMap[K comparable, V any] struct {
	data map[K]V
}

func NewSafeMap[K comparable, V any]() *SafeMap[K, V] {
	return &SafeMap[K, V]{data: make(map[K]V)}
}

func (sm *SafeMap[K, V]) Set(key K, value V) {
	sm.data[key] = value
}

func (sm *SafeMap[K, V]) Get(key K) (V, bool) {
	value, ok := sm.data[key]
	return value, ok
}

func (sm *SafeMap[K, V]) Delete(key K) {
	delete(sm.data, key)
}

func (sm *SafeMap[K, V]) Size() int {
	return len(sm.data)
}

func (sm *SafeMap[K, V]) Keys() []K {
	keys := make([]K, 0, len(sm.data))
	for k := range sm.data {
		keys = append(keys, k)
	}
	return keys
}

// 泛型结果类型
type Result[T any] struct {
	value T
	err   error
}

func Ok[T any](value T) Result[T] {
	return Result[T]{value: value}
}

func Err[T any](err error) Result[T] {
	var zero T
	return Result[T]{value: zero, err: err}
}

func (r Result[T]) IsOk() bool {
	return r.err == nil
}

func (r Result[T]) IsErr() bool {
	return r.err != nil
}

func (r Result[T]) Unwrap() T {
	if r.err != nil {
		panic("尝试解包错误结果")
	}
	return r.value
}

func (r Result[T]) Error() string {
	if r.err == nil {
		return ""
	}
	return r.err.Error()
}

// 使用Result的除法函数
func Divide(a, b float64) Result[float64] {
	if b == 0 {
		return Err[float64](fmt.Errorf("除数不能为零"))
	}
	return Ok(a / b)
}

// 演示类型推断
func demonstrateTypeInference() {
	fmt.Println("\n--- 类型推断 ---")
	
	// 1. 函数参数类型推断
	fmt.Println("1. 函数参数类型推断:")
	
	// 编译器可以从参数推断类型
	result1 := Max(10, 20)        // 推断为int
	result2 := Max(3.14, 2.71)    // 推断为float64
	result3 := Max("a", "b")      // 推断为string
	
	fmt.Printf("Max(10, 20) = %d\n", result1)
	fmt.Printf("Max(3.14, 2.71) = %.2f\n", result2)
	fmt.Printf("Max(\"a\", \"b\") = %s\n", result3)
	
	// 2. 显式类型指定
	fmt.Println("\n2. 显式类型指定:")
	
	// 有时需要显式指定类型
	emptySlice := make([]string, 0) // 需要指定类型
	fmt.Printf("空切片: %v\n", emptySlice)
	
	// 3. 复杂类型推断
	fmt.Println("\n3. 复杂类型推断:")
	
	numbers := []int{1, 2, 3, 4, 5}
	strings := Map(numbers, func(x int) string {
		return strconv.Itoa(x)
	})
	fmt.Printf("数字转字符串: %v\n", strings)
}

// 演示实用示例
func demonstratePracticalExamples() {
	fmt.Println("\n--- 实用示例 ---")
	
	// 1. 泛型排序
	fmt.Println("1. 泛型排序:")
	
	numbers := []int{3, 1, 4, 1, 5, 9, 2, 6}
	sortedNumbers := SortSlice(numbers)
	fmt.Printf("排序前: %v\n", numbers)
	fmt.Printf("排序后: %v\n", sortedNumbers)
	
	words := []string{"banana", "apple", "cherry", "date"}
	sortedWords := SortSlice(words)
	fmt.Printf("排序前: %v\n", words)
	fmt.Printf("排序后: %v\n", sortedWords)
	
	// 2. 泛型过滤
	fmt.Println("\n2. 泛型过滤:")
	
	numbers2 := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
	evens := Filter(numbers2, func(x int) bool { return x%2 == 0 })
	fmt.Printf("原始: %v\n", numbers2)
	fmt.Printf("偶数: %v\n", evens)
	
	words2 := []string{"apple", "banana", "cherry", "date", "elderberry"}
	longWords := Filter(words2, func(s string) bool { return len(s) > 5 })
	fmt.Printf("原始: %v\n", words2)
	fmt.Printf("长单词: %v\n", longWords)
	
	// 3. 泛型归约
	fmt.Println("\n3. 泛型归约:")
	
	numbers3 := []int{1, 2, 3, 4, 5}
	sum := Reduce(numbers3, 0, func(acc, x int) int { return acc + x })
	product := Reduce(numbers3, 1, func(acc, x int) int { return acc * x })
	
	fmt.Printf("数字: %v\n", numbers3)
	fmt.Printf("求和: %d\n", sum)
	fmt.Printf("求积: %d\n", product)
	
	words3 := []string{"Hello", " ", "World", "!"}
	sentence := Reduce(words3, "", func(acc, s string) string { return acc + s })
	fmt.Printf("单词: %v\n", words3)
	fmt.Printf("句子: %s\n", sentence)
	
	// 4. 泛型缓存
	fmt.Println("\n4. 泛型缓存:")
	
	cache := NewCache[string, int]()
	cache.Set("apple", 5)
	cache.Set("banana", 3)
	
	if value, found := cache.Get("apple"); found {
		fmt.Printf("缓存中的apple: %d\n", value)
	}
	
	fmt.Printf("缓存大小: %d\n", cache.Size())
	
	cache.Delete("banana")
	fmt.Printf("删除banana后缓存大小: %d\n", cache.Size())
}

// 泛型排序
func SortSlice[T Ordered](slice []T) []T {
	result := make([]T, len(slice))
	copy(result, slice)
	sort.Slice(result, func(i, j int) bool {
		return result[i] < result[j]
	})
	return result
}

// 泛型过滤
func Filter[T any](slice []T, predicate func(T) bool) []T {
	var result []T
	for _, v := range slice {
		if predicate(v) {
			result = append(result, v)
		}
	}
	return result
}

// 泛型归约
func Reduce[T, U any](slice []T, initial U, reducer func(U, T) U) U {
	result := initial
	for _, v := range slice {
		result = reducer(result, v)
	}
	return result
}

// 泛型缓存
type Cache[K comparable, V any] struct {
	data map[K]V
}

func NewCache[K comparable, V any]() *Cache[K, V] {
	return &Cache[K, V]{data: make(map[K]V)}
}

func (c *Cache[K, V]) Set(key K, value V) {
	c.data[key] = value
}

func (c *Cache[K, V]) Get(key K) (V, bool) {
	value, ok := c.data[key]
	return value, ok
}

func (c *Cache[K, V]) Delete(key K) {
	delete(c.data, key)
}

func (c *Cache[K, V]) Size() int {
	return len(c.data)
}

func (c *Cache[K, V]) Clear() {
	c.data = make(map[K]V)
}

/*
运行此程序的命令：
go run advanced/05_generics.go

学习要点：
1. 泛型允许编写类型安全的通用代码，减少代码重复
2. 类型约束定义了泛型参数可以使用的类型集合
3. any是interface{}的别名，comparable约束可比较类型
4. 类型推断让编译器自动推断泛型参数类型
5. 泛型类型可以创建类型安全的数据结构
6. 泛型函数可以处理多种类型而保持类型安全
7. 合理使用泛型可以提高代码复用性和类型安全性
8. 过度使用泛型可能导致代码复杂化，需要权衡
*/
