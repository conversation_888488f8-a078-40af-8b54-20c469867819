package main

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"
)

/*
Go语言进阶特性 - 上下文（Context）使用

本文件涵盖：
1. Context的基本概念和用途
2. Context的创建和使用
3. 超时控制
4. 取消操作
5. 值传递
6. Context的最佳实践
*/

func main() {
	fmt.Println("=== Go语言Context学习 ===")
	
	// 1. Context基础
	demonstrateContextBasics()
	
	// 2. 超时控制
	demonstrateTimeout()
	
	// 3. 取消操作
	demonstrateCancellation()
	
	// 4. 值传递
	demonstrateValuePassing()
	
	// 5. Context链
	demonstrateContextChaining()
	
	// 6. 实用示例
	demonstratePracticalExamples()
}

// 演示Context基础
func demonstrateContextBasics() {
	fmt.Println("\n--- Context基础 ---")
	
	// 1. 背景Context
	fmt.Println("1. 背景Context:")
	
	// context.Background() 是所有Context的根
	ctx := context.Background()
	fmt.Printf("背景Context: %v\n", ctx)
	
	// context.TODO() 用于不确定使用哪个Context的情况
	todoCtx := context.TODO()
	fmt.Printf("TODO Context: %v\n", todoCtx)
	
	// 2. Context的基本方法
	fmt.Println("\n2. Context的基本方法:")
	
	// Deadline() 返回Context的截止时间
	deadline, ok := ctx.Deadline()
	fmt.Printf("是否有截止时间: %t, 截止时间: %v\n", ok, deadline)
	
	// Done() 返回一个channel，当Context被取消时关闭
	select {
	case <-ctx.Done():
		fmt.Println("Context已取消")
	default:
		fmt.Println("Context未取消")
	}
	
	// Err() 返回Context被取消的原因
	fmt.Printf("Context错误: %v\n", ctx.Err())
	
	// Value() 获取Context中的值
	value := ctx.Value("key")
	fmt.Printf("Context中的值: %v\n", value)
}

// 演示超时控制
func demonstrateTimeout() {
	fmt.Println("\n--- 超时控制 ---")
	
	// 1. WithTimeout
	fmt.Println("1. WithTimeout:")
	
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel() // 确保资源被释放
	
	go longRunningTask(ctx, "任务1", 1*time.Second)
	go longRunningTask(ctx, "任务2", 3*time.Second) // 这个会超时
	
	time.Sleep(4 * time.Second)
	
	// 2. WithDeadline
	fmt.Println("\n2. WithDeadline:")
	
	deadline := time.Now().Add(1500 * time.Millisecond)
	ctx2, cancel2 := context.WithDeadline(context.Background(), deadline)
	defer cancel2()
	
	go longRunningTask(ctx2, "任务3", 1*time.Second)
	go longRunningTask(ctx2, "任务4", 2*time.Second) // 这个会超时
	
	time.Sleep(3 * time.Second)
	
	// 3. 检查超时
	fmt.Println("\n3. 检查超时:")
	
	result := performTaskWithTimeout(1 * time.Second)
	fmt.Printf("1秒超时任务结果: %s\n", result)
	
	result = performTaskWithTimeout(500 * time.Millisecond)
	fmt.Printf("500毫秒超时任务结果: %s\n", result)
}

// 长时间运行的任务
func longRunningTask(ctx context.Context, name string, duration time.Duration) {
	fmt.Printf("%s 开始执行\n", name)
	
	select {
	case <-time.After(duration):
		fmt.Printf("%s 正常完成\n", name)
	case <-ctx.Done():
		fmt.Printf("%s 被取消: %v\n", name, ctx.Err())
	}
}

// 带超时的任务执行
func performTaskWithTimeout(timeout time.Duration) string {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	
	result := make(chan string, 1)
	
	go func() {
		// 模拟工作
		time.Sleep(800 * time.Millisecond)
		result <- "任务完成"
	}()
	
	select {
	case res := <-result:
		return res
	case <-ctx.Done():
		return fmt.Sprintf("任务超时: %v", ctx.Err())
	}
}

// 演示取消操作
func demonstrateCancellation() {
	fmt.Println("\n--- 取消操作 ---")
	
	// 1. WithCancel
	fmt.Println("1. WithCancel:")
	
	ctx, cancel := context.WithCancel(context.Background())
	
	// 启动多个goroutine
	var wg sync.WaitGroup
	for i := 1; i <= 3; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			cancellableWorker(ctx, fmt.Sprintf("工作者%d", id))
		}(i)
	}
	
	// 2秒后取消所有工作者
	time.Sleep(2 * time.Second)
	fmt.Println("取消所有工作者...")
	cancel()
	
	wg.Wait()
	fmt.Println("所有工作者已停止")
	
	// 2. 级联取消
	fmt.Println("\n2. 级联取消:")
	
	parentCtx, parentCancel := context.WithCancel(context.Background())
	childCtx, childCancel := context.WithCancel(parentCtx)
	
	go func() {
		select {
		case <-childCtx.Done():
			fmt.Printf("子Context被取消: %v\n", childCtx.Err())
		}
	}()
	
	// 取消父Context会自动取消子Context
	time.Sleep(100 * time.Millisecond)
	parentCancel()
	time.Sleep(100 * time.Millisecond)
	
	// 清理
	childCancel()
	
	// 3. 优雅关闭
	fmt.Println("\n3. 优雅关闭:")
	
	server := NewServer()
	go server.Start()
	
	time.Sleep(1 * time.Second)
	fmt.Println("关闭服务器...")
	server.Shutdown()
	
	time.Sleep(500 * time.Millisecond)
}

// 可取消的工作者
func cancellableWorker(ctx context.Context, name string) {
	fmt.Printf("%s 开始工作\n", name)
	
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			fmt.Printf("%s 正在工作...\n", name)
		case <-ctx.Done():
			fmt.Printf("%s 收到取消信号，停止工作: %v\n", name, ctx.Err())
			return
		}
	}
}

// 简单的服务器示例
type Server struct {
	ctx    context.Context
	cancel context.CancelFunc
}

func NewServer() *Server {
	ctx, cancel := context.WithCancel(context.Background())
	return &Server{ctx: ctx, cancel: cancel}
}

func (s *Server) Start() {
	fmt.Println("服务器启动")
	
	ticker := time.NewTicker(300 * time.Millisecond)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			fmt.Println("服务器处理请求...")
		case <-s.ctx.Done():
			fmt.Println("服务器优雅关闭")
			return
		}
	}
}

func (s *Server) Shutdown() {
	s.cancel()
}

// 演示值传递
func demonstrateValuePassing() {
	fmt.Println("\n--- 值传递 ---")
	
	// 1. WithValue
	fmt.Println("1. WithValue:")
	
	// 定义键类型（避免冲突）
	type contextKey string
	const (
		userIDKey    contextKey = "userID"
		requestIDKey contextKey = "requestID"
	)
	
	// 创建带值的Context
	ctx := context.WithValue(context.Background(), userIDKey, "user123")
	ctx = context.WithValue(ctx, requestIDKey, "req456")
	
	// 传递Context到函数
	processRequest(ctx)
	
	// 2. 值的继承
	fmt.Println("\n2. 值的继承:")
	
	// 子Context继承父Context的值
	childCtx, cancel := context.WithTimeout(ctx, 1*time.Second)
	defer cancel()
	
	fmt.Println("子Context中的值:")
	if userID := childCtx.Value(userIDKey); userID != nil {
		fmt.Printf("  用户ID: %v\n", userID)
	}
	if requestID := childCtx.Value(requestIDKey); requestID != nil {
		fmt.Printf("  请求ID: %v\n", requestID)
	}
	
	// 3. 类型安全的值访问
	fmt.Println("\n3. 类型安全的值访问:")
	
	userID := getUserID(ctx)
	requestID := getRequestID(ctx)
	
	fmt.Printf("类型安全获取 - 用户ID: %s, 请求ID: %s\n", userID, requestID)
}

// 处理请求的函数
func processRequest(ctx context.Context) {
	fmt.Println("处理请求:")
	
	// 从Context中获取值
	if userID := ctx.Value("userID"); userID != nil {
		fmt.Printf("  用户ID: %v\n", userID)
	}
	
	if requestID := ctx.Value("requestID"); requestID != nil {
		fmt.Printf("  请求ID: %v\n", requestID)
	}
	
	// 调用其他函数，传递Context
	authenticateUser(ctx)
	logRequest(ctx)
}

func authenticateUser(ctx context.Context) {
	if userID := ctx.Value("userID"); userID != nil {
		fmt.Printf("  认证用户: %v\n", userID)
	}
}

func logRequest(ctx context.Context) {
	if requestID := ctx.Value("requestID"); requestID != nil {
		fmt.Printf("  记录请求: %v\n", requestID)
	}
}

// 类型安全的值访问函数
func getUserID(ctx context.Context) string {
	if userID := ctx.Value("userID"); userID != nil {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return ""
}

func getRequestID(ctx context.Context) string {
	if requestID := ctx.Value("requestID"); requestID != nil {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

// 演示Context链
func demonstrateContextChaining() {
	fmt.Println("\n--- Context链 ---")
	
	// 1. 多层Context
	fmt.Println("1. 多层Context:")
	
	// 根Context
	rootCtx := context.Background()
	
	// 添加值
	ctx1 := context.WithValue(rootCtx, "level", "1")
	
	// 添加超时
	ctx2, cancel2 := context.WithTimeout(ctx1, 3*time.Second)
	defer cancel2()
	
	// 添加取消
	ctx3, cancel3 := context.WithCancel(ctx2)
	defer cancel3()
	
	// 添加更多值
	ctx4 := context.WithValue(ctx3, "operation", "chain-demo")
	
	// 使用最终的Context
	chainedOperation(ctx4)
	
	// 2. Context传播
	fmt.Println("\n2. Context传播:")
	
	// 模拟HTTP请求处理链
	requestCtx := context.WithValue(context.Background(), "traceID", "trace123")
	requestCtx, cancel := context.WithTimeout(requestCtx, 2*time.Second)
	defer cancel()
	
	handleHTTPRequest(requestCtx)
}

func chainedOperation(ctx context.Context) {
	fmt.Println("链式操作:")
	
	// 检查所有值
	if level := ctx.Value("level"); level != nil {
		fmt.Printf("  级别: %v\n", level)
	}
	if operation := ctx.Value("operation"); operation != nil {
		fmt.Printf("  操作: %v\n", operation)
	}
	
	// 检查截止时间
	if deadline, ok := ctx.Deadline(); ok {
		fmt.Printf("  截止时间: %v\n", deadline.Format("15:04:05"))
	}
	
	// 模拟工作
	select {
	case <-time.After(500 * time.Millisecond):
		fmt.Println("  链式操作完成")
	case <-ctx.Done():
		fmt.Printf("  链式操作被取消: %v\n", ctx.Err())
	}
}

// 模拟HTTP请求处理
func handleHTTPRequest(ctx context.Context) {
	fmt.Println("处理HTTP请求:")
	
	// 中间件1：认证
	ctx = authMiddleware(ctx)
	
	// 中间件2：日志
	ctx = loggingMiddleware(ctx)
	
	// 业务处理
	businessLogic(ctx)
}

func authMiddleware(ctx context.Context) context.Context {
	fmt.Println("  认证中间件")
	// 添加用户信息到Context
	return context.WithValue(ctx, "userID", "auth_user_123")
}

func loggingMiddleware(ctx context.Context) context.Context {
	fmt.Println("  日志中间件")
	if traceID := ctx.Value("traceID"); traceID != nil {
		fmt.Printf("    追踪ID: %v\n", traceID)
	}
	return ctx
}

func businessLogic(ctx context.Context) {
	fmt.Println("  业务逻辑")
	
	// 检查用户
	if userID := ctx.Value("userID"); userID != nil {
		fmt.Printf("    处理用户: %v\n", userID)
	}
	
	// 模拟数据库查询
	if err := queryDatabase(ctx); err != nil {
		fmt.Printf("    数据库查询失败: %v\n", err)
	}
}

func queryDatabase(ctx context.Context) error {
	select {
	case <-time.After(100 * time.Millisecond):
		fmt.Println("    数据库查询成功")
		return nil
	case <-ctx.Done():
		return fmt.Errorf("数据库查询被取消: %w", ctx.Err())
	}
}

// 演示实用示例
func demonstratePracticalExamples() {
	fmt.Println("\n--- 实用示例 ---")
	
	// 1. 并发任务管理
	fmt.Println("1. 并发任务管理:")
	
	taskManager := NewTaskManager()
	
	// 启动任务
	taskManager.StartTask("task1", 2*time.Second)
	taskManager.StartTask("task2", 1*time.Second)
	taskManager.StartTask("task3", 3*time.Second)
	
	// 等待一段时间后停止所有任务
	time.Sleep(1500 * time.Millisecond)
	fmt.Println("停止所有任务...")
	taskManager.StopAll()
	
	time.Sleep(500 * time.Millisecond)
	
	// 2. 请求重试
	fmt.Println("\n2. 请求重试:")
	
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	result, err := retryableOperation(ctx, 3)
	if err != nil {
		fmt.Printf("操作失败: %v\n", err)
	} else {
		fmt.Printf("操作成功: %s\n", result)
	}
	
	// 3. 管道处理
	fmt.Println("\n3. 管道处理:")
	
	ctx2, cancel2 := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel2()
	
	pipeline := NewPipeline(ctx2)
	pipeline.Start()
	
	time.Sleep(3 * time.Second)
}

// 任务管理器
type TaskManager struct {
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

func NewTaskManager() *TaskManager {
	ctx, cancel := context.WithCancel(context.Background())
	return &TaskManager{ctx: ctx, cancel: cancel}
}

func (tm *TaskManager) StartTask(name string, duration time.Duration) {
	tm.wg.Add(1)
	go func() {
		defer tm.wg.Done()
		tm.runTask(name, duration)
	}()
}

func (tm *TaskManager) runTask(name string, duration time.Duration) {
	fmt.Printf("任务 %s 开始\n", name)
	
	select {
	case <-time.After(duration):
		fmt.Printf("任务 %s 完成\n", name)
	case <-tm.ctx.Done():
		fmt.Printf("任务 %s 被取消\n", name)
	}
}

func (tm *TaskManager) StopAll() {
	tm.cancel()
	tm.wg.Wait()
}

// 可重试的操作
func retryableOperation(ctx context.Context, maxRetries int) (string, error) {
	for i := 0; i < maxRetries; i++ {
		select {
		case <-ctx.Done():
			return "", ctx.Err()
		default:
		}
		
		fmt.Printf("尝试 %d/%d\n", i+1, maxRetries)
		
		// 模拟操作
		if rand.Float32() < 0.7 { // 70%成功率
			return "操作成功", nil
		}
		
		if i < maxRetries-1 {
			// 等待后重试
			select {
			case <-time.After(500 * time.Millisecond):
			case <-ctx.Done():
				return "", ctx.Err()
			}
		}
	}
	
	return "", fmt.Errorf("重试 %d 次后仍然失败", maxRetries)
}

// 管道处理
type Pipeline struct {
	ctx context.Context
}

func NewPipeline(ctx context.Context) *Pipeline {
	return &Pipeline{ctx: ctx}
}

func (p *Pipeline) Start() {
	// 阶段1：生成数据
	dataCh := p.generateData()
	
	// 阶段2：处理数据
	processedCh := p.processData(dataCh)
	
	// 阶段3：输出结果
	p.outputResults(processedCh)
}

func (p *Pipeline) generateData() <-chan int {
	ch := make(chan int)
	
	go func() {
		defer close(ch)
		for i := 1; i <= 10; i++ {
			select {
			case ch <- i:
				fmt.Printf("生成数据: %d\n", i)
				time.Sleep(200 * time.Millisecond)
			case <-p.ctx.Done():
				fmt.Println("数据生成被取消")
				return
			}
		}
	}()
	
	return ch
}

func (p *Pipeline) processData(input <-chan int) <-chan int {
	ch := make(chan int)
	
	go func() {
		defer close(ch)
		for data := range input {
			select {
			case ch <- data * 2:
				fmt.Printf("处理数据: %d -> %d\n", data, data*2)
			case <-p.ctx.Done():
				fmt.Println("数据处理被取消")
				return
			}
		}
	}()
	
	return ch
}

func (p *Pipeline) outputResults(input <-chan int) {
	go func() {
		for result := range input {
			select {
			case <-p.ctx.Done():
				fmt.Println("结果输出被取消")
				return
			default:
				fmt.Printf("输出结果: %d\n", result)
			}
		}
		fmt.Println("管道处理完成")
	}()
}

/*
运行此程序的命令：
go run advanced/06_context.go

学习要点：
1. Context用于在goroutine之间传递取消信号、超时和值
2. 四种创建Context的方法：Background、TODO、WithCancel、WithTimeout/WithDeadline、WithValue
3. Context是不可变的，每次操作都会创建新的Context
4. Context的取消会级联传播到所有子Context
5. 使用Context.Value传递请求范围的数据，但不要滥用
6. 总是在defer中调用cancel函数释放资源
7. Context应该作为函数的第一个参数传递
8. Context是并发安全的，可以在多个goroutine中使用
*/
