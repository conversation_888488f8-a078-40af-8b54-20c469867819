package main

import (
	"testing"
)

/*
Go语言测试文件示例

运行测试命令：
go test advanced/07_testing_test.go advanced/07_testing.go -v
go test -bench=. advanced/07_testing_test.go advanced/07_testing.go
go test -cover advanced/07_testing_test.go advanced/07_testing.go
*/

// ============ 基本单元测试 ============

func TestAdd(t *testing.T) {
	result := Add(2, 3)
	expected := 5
	if result != expected {
		t.<PERSON>("Add(2, 3) = %d; want %d", result, expected)
	}
}

func TestSubtract(t *testing.T) {
	result := Subtract(5, 3)
	expected := 2
	if result != expected {
		t.<PERSON><PERSON><PERSON>("Subtract(5, 3) = %d; want %d", result, expected)
	}
}

func TestDivide(t *testing.T) {
	// 正常情况
	result, err := Divide(10, 2)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Divide(10, 2) returned error: %v", err)
	}
	if result != 5.0 {
		t.<PERSON><PERSON><PERSON>("Divide(10, 2) = %f; want 5.0", result)
	}
	
	// 除零错误
	_, err = Divide(10, 0)
	if err == nil {
		t.<PERSON>rror("Divide(10, 0) should return error")
	}
}

// ============ 表格驱动测试 ============

func TestIsPalindrome(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{"empty string", "", true},
		{"single char", "a", true},
		{"palindrome", "racecar", true},
		{"palindrome with caps", "RaceCar", true},
		{"not palindrome", "hello", false},
		{"palindrome sentence", "A man a plan a canal Panama", false}, // 简化版本不处理空格
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsPalindrome(tt.input)
			if result != tt.expected {
				t.Errorf("IsPalindrome(%q) = %v; want %v", tt.input, result, tt.expected)
			}
		})
	}
}

func TestWordCount(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected int
	}{
		{"empty string", "", 0},
		{"single word", "hello", 1},
		{"multiple words", "hello world", 2},
		{"multiple spaces", "hello   world", 2},
		{"leading/trailing spaces", "  hello world  ", 2},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := WordCount(tt.input)
			if result != tt.expected {
				t.Errorf("WordCount(%q) = %d; want %d", tt.input, result, tt.expected)
			}
		})
	}
}

func TestMax(t *testing.T) {
	tests := []struct {
		name     string
		input    []int
		expected int
	}{
		{"empty slice", []int{}, 0},
		{"single element", []int{5}, 5},
		{"multiple elements", []int{1, 3, 2, 5, 4}, 5},
		{"negative numbers", []int{-1, -3, -2}, -1},
		{"mixed numbers", []int{-1, 3, -2, 5, 4}, 5},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Max(tt.input)
			if result != tt.expected {
				t.Errorf("Max(%v) = %d; want %d", tt.input, result, tt.expected)
			}
		})
	}
}

// ============ 结构体测试 ============

func TestUser_Validate(t *testing.T) {
	tests := []struct {
		name    string
		user    User
		wantErr bool
	}{
		{
			name: "valid user",
			user: User{ID: 1, Name: "John", Email: "<EMAIL>", Age: 25},
			wantErr: false,
		},
		{
			name: "empty name",
			user: User{ID: 1, Name: "", Email: "<EMAIL>", Age: 25},
			wantErr: true,
		},
		{
			name: "empty email",
			user: User{ID: 1, Name: "John", Email: "", Age: 25},
			wantErr: true,
		},
		{
			name: "invalid email",
			user: User{ID: 1, Name: "John", Email: "invalid-email", Age: 25},
			wantErr: true,
		},
		{
			name: "negative age",
			user: User{ID: 1, Name: "John", Email: "<EMAIL>", Age: -1},
			wantErr: true,
		},
		{
			name: "too old",
			user: User{ID: 1, Name: "John", Email: "<EMAIL>", Age: 200},
			wantErr: true,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.user.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("User.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestUserManager(t *testing.T) {
	um := NewUserManager()
	
	// 测试添加用户
	user := &User{ID: 1, Name: "John", Email: "<EMAIL>", Age: 25}
	err := um.AddUser(user)
	if err != nil {
		t.Errorf("AddUser() error = %v", err)
	}
	
	// 测试获取用户
	foundUser, exists := um.GetUser(1)
	if !exists {
		t.Error("GetUser() should find the user")
	}
	if foundUser.Name != "John" {
		t.Errorf("GetUser() name = %s; want John", foundUser.Name)
	}
	
	// 测试用户数量
	count := um.GetUserCount()
	if count != 1 {
		t.Errorf("GetUserCount() = %d; want 1", count)
	}
	
	// 测试删除用户
	err = um.DeleteUser(1)
	if err != nil {
		t.Errorf("DeleteUser() error = %v", err)
	}
	
	// 验证用户已删除
	_, exists = um.GetUser(1)
	if exists {
		t.Error("GetUser() should not find deleted user")
	}
}

// ============ 基准测试 ============

func BenchmarkBubbleSort(b *testing.B) {
	data := []int{64, 34, 25, 12, 22, 11, 90}
	
	for i := 0; i < b.N; i++ {
		BubbleSort(data)
	}
}

func BenchmarkQuickSort(b *testing.B) {
	data := []int{64, 34, 25, 12, 22, 11, 90}
	
	for i := 0; i < b.N; i++ {
		QuickSort(data)
	}
}

func BenchmarkSum(b *testing.B) {
	data := make([]int, 1000)
	for i := range data {
		data[i] = i
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Sum(data)
	}
}

// ============ 示例测试 ============

func ExampleAdd() {
	result := Add(2, 3)
	fmt.Println(result)
	// Output: 5
}

func ExampleIsPalindrome() {
	fmt.Println(IsPalindrome("racecar"))
	fmt.Println(IsPalindrome("hello"))
	// Output:
	// true
	// false
}

func ExampleReverse() {
	result := Reverse("hello")
	fmt.Println(result)
	// Output: olleh
}

// ============ 子测试 ============

func TestCalculator(t *testing.T) {
	calc := &Calculator{}
	
	t.Run("Add", func(t *testing.T) {
		result := calc.Add(2, 3)
		if result != 5 {
			t.Errorf("Add(2, 3) = %d; want 5", result)
		}
	})
	
	t.Run("Subtract", func(t *testing.T) {
		result := calc.Subtract(5, 3)
		if result != 2 {
			t.Errorf("Subtract(5, 3) = %d; want 2", result)
		}
	})
	
	t.Run("History", func(t *testing.T) {
		history := calc.GetHistory()
		if len(history) != 2 {
			t.Errorf("History length = %d; want 2", len(history))
		}
	})
}

// ============ 错误处理测试 ============

func TestStack(t *testing.T) {
	stack := NewStack()
	
	// 测试空栈
	if !stack.IsEmpty() {
		t.Error("New stack should be empty")
	}
	
	// 测试空栈弹出
	_, err := stack.Pop()
	if err == nil {
		t.Error("Pop from empty stack should return error")
	}
	
	// 测试入栈
	stack.Push(1)
	stack.Push(2)
	stack.Push(3)
	
	if stack.Size() != 3 {
		t.Errorf("Stack size = %d; want 3", stack.Size())
	}
	
	// 测试查看栈顶
	top, err := stack.Peek()
	if err != nil {
		t.Errorf("Peek() error = %v", err)
	}
	if top != 3 {
		t.Errorf("Peek() = %d; want 3", top)
	}
	
	// 测试出栈
	item, err := stack.Pop()
	if err != nil {
		t.Errorf("Pop() error = %v", err)
	}
	if item != 3 {
		t.Errorf("Pop() = %d; want 3", item)
	}
	
	if stack.Size() != 2 {
		t.Errorf("Stack size after pop = %d; want 2", stack.Size())
	}
}

// ============ 辅助函数 ============

func assertEqual(t *testing.T, actual, expected interface{}) {
	t.Helper()
	if actual != expected {
		t.Errorf("got %v; want %v", actual, expected)
	}
}

func assertNoError(t *testing.T, err error) {
	t.Helper()
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
}

func assertError(t *testing.T, err error) {
	t.Helper()
	if err == nil {
		t.Error("expected error but got nil")
	}
}

// ============ 并行测试 ============

func TestParallel(t *testing.T) {
	t.Run("group", func(t *testing.T) {
		t.Run("Test1", func(t *testing.T) {
			t.Parallel()
			// 测试逻辑
			result := Add(1, 1)
			assertEqual(t, result, 2)
		})
		
		t.Run("Test2", func(t *testing.T) {
			t.Parallel()
			// 测试逻辑
			result := Multiply(2, 3)
			assertEqual(t, result, 6)
		})
	})
}

/*
测试最佳实践：

1. 测试文件以 _test.go 结尾
2. 测试函数以 Test 开头，接受 *testing.T 参数
3. 基准测试函数以 Benchmark 开头，接受 *testing.B 参数
4. 示例测试函数以 Example 开头
5. 使用表格驱动测试处理多个测试用例
6. 使用子测试组织相关测试
7. 使用 t.Helper() 标记辅助函数
8. 使用 t.Parallel() 并行运行测试
9. 测试边界条件和错误情况
10. 保持测试简单和独立
*/
