package main

import (
	"errors"
	"fmt"
	"io"
	"os"
	"strconv"
	"time"
)

/*
Go语言进阶特性 - 错误处理

本文件涵盖：
1. error接口和基本错误处理
2. 自定义错误类型
3. 错误包装和解包
4. 错误处理的最佳实践
5. panic和recover机制
6. 实用示例和模式
*/

func main() {
	fmt.Println("=== Go语言错误处理学习 ===")
	
	// 1. 基本错误处理
	demonstrateBasicErrorHandling()
	
	// 2. 自定义错误
	demonstrateCustomErrors()
	
	// 3. 错误包装
	demonstrateErrorWrapping()
	
	// 4. panic和recover
	demonstratePanicRecover()
	
	// 5. 错误处理模式
	demonstrateErrorPatterns()
	
	// 6. 实用示例
	demonstratePracticalExamples()
}

// 演示基本错误处理
func demonstrateBasicErrorHandling() {
	fmt.Println("\n--- 基本错误处理 ---")
	
	// 1. 使用errors.New创建错误
	err1 := errors.New("这是一个简单的错误")
	fmt.Printf("错误1: %v\n", err1)
	
	// 2. 使用fmt.Errorf创建格式化错误
	name := "张三"
	age := -5
	err2 := fmt.Errorf("用户 %s 的年龄 %d 无效", name, age)
	fmt.Printf("错误2: %v\n", err2)
	
	// 3. 函数返回错误的标准模式
	result, err := divide(10, 2)
	if err != nil {
		fmt.Printf("除法错误: %v\n", err)
	} else {
		fmt.Printf("10 ÷ 2 = %.2f\n", result)
	}
	
	result, err = divide(10, 0)
	if err != nil {
		fmt.Printf("除法错误: %v\n", err)
	} else {
		fmt.Printf("10 ÷ 0 = %.2f\n", result)
	}
	
	// 4. 错误检查的简化写法
	if result, err := divide(15, 3); err != nil {
		fmt.Printf("错误: %v\n", err)
	} else {
		fmt.Printf("15 ÷ 3 = %.2f\n", result)
	}
	
	// 5. 忽略错误（不推荐，除非确定不会出错）
	result, _ = divide(20, 4)
	fmt.Printf("20 ÷ 4 = %.2f (忽略错误检查)\n", result)
	
	// 6. 多个返回值的错误处理
	content, err := readFile("example.txt")
	if err != nil {
		fmt.Printf("读取文件错误: %v\n", err)
	} else {
		fmt.Printf("文件内容: %s\n", content)
	}
}

// 基本的除法函数
func divide(a, b float64) (float64, error) {
	if b == 0 {
		return 0, errors.New("除数不能为零")
	}
	return a / b, nil
}

// 模拟读取文件
func readFile(filename string) (string, error) {
	if filename == "" {
		return "", errors.New("文件名不能为空")
	}
	if filename == "example.txt" {
		return "", fmt.Errorf("文件 %s 不存在", filename)
	}
	return "文件内容", nil
}

// 演示自定义错误
func demonstrateCustomErrors() {
	fmt.Println("\n--- 自定义错误 ---")
	
	// 1. 简单的自定义错误类型
	err := ValidationError{Field: "age", Value: -5, Message: "年龄不能为负数"}
	fmt.Printf("验证错误: %v\n", err)
	
	// 2. 带有更多信息的错误类型
	netErr := NetworkError{
		Operation: "HTTP GET",
		URL:       "https://api.example.com/users",
		Err:       errors.New("连接超时"),
		Timestamp: time.Now(),
	}
	fmt.Printf("网络错误: %v\n", netErr)
	
	// 3. 使用自定义错误的函数
	user, err := validateUser("", 25, "<EMAIL>")
	if err != nil {
		// 类型断言检查具体错误类型
		if valErr, ok := err.(*ValidationError); ok {
			fmt.Printf("验证失败 - 字段: %s, 值: %v, 消息: %s\n", 
				valErr.Field, valErr.Value, valErr.Message)
		} else {
			fmt.Printf("其他错误: %v\n", err)
		}
	} else {
		fmt.Printf("用户验证成功: %+v\n", user)
	}
	
	// 4. 错误类型的方法
	fmt.Printf("错误是否为临时错误: %t\n", netErr.IsTemporary())
	fmt.Printf("错误是否为超时错误: %t\n", netErr.IsTimeout())
	
	// 5. 实现多个接口的错误
	timeoutErr := TimeoutError{
		Operation: "数据库查询",
		Duration:  5 * time.Second,
		Timeout:   3 * time.Second,
	}
	
	fmt.Printf("超时错误: %v\n", timeoutErr)
	fmt.Printf("是否为临时错误: %t\n", timeoutErr.IsTemporary())
	fmt.Printf("是否为超时错误: %t\n", timeoutErr.IsTimeout())
}

// 自定义错误类型1：验证错误
type ValidationError struct {
	Field   string
	Value   interface{}
	Message string
}

func (e *ValidationError) Error() string {
	return fmt.Sprintf("验证错误: 字段 '%s' 的值 '%v' %s", e.Field, e.Value, e.Message)
}

// 自定义错误类型2：网络错误
type NetworkError struct {
	Operation string
	URL       string
	Err       error
	Timestamp time.Time
}

func (e *NetworkError) Error() string {
	return fmt.Sprintf("网络错误 [%s]: %s 在 %s 时发生错误: %v", 
		e.Timestamp.Format("15:04:05"), e.Operation, e.URL, e.Err)
}

func (e *NetworkError) Unwrap() error {
	return e.Err
}

func (e *NetworkError) IsTemporary() bool {
	return true // 网络错误通常是临时的
}

func (e *NetworkError) IsTimeout() bool {
	return e.Err != nil && e.Err.Error() == "连接超时"
}

// 自定义错误类型3：超时错误
type TimeoutError struct {
	Operation string
	Duration  time.Duration
	Timeout   time.Duration
}

func (e TimeoutError) Error() string {
	return fmt.Sprintf("操作 '%s' 超时: 执行时间 %v 超过了限制 %v", 
		e.Operation, e.Duration, e.Timeout)
}

func (e TimeoutError) IsTemporary() bool {
	return true
}

func (e TimeoutError) IsTimeout() bool {
	return true
}

// 用户结构体
type User struct {
	Name  string
	Age   int
	Email string
}

// 用户验证函数
func validateUser(name string, age int, email string) (*User, error) {
	if name == "" {
		return nil, &ValidationError{
			Field:   "name",
			Value:   name,
			Message: "不能为空",
		}
	}
	
	if age < 0 || age > 150 {
		return nil, &ValidationError{
			Field:   "age",
			Value:   age,
			Message: "必须在0-150之间",
		}
	}
	
	if email == "" {
		return nil, &ValidationError{
			Field:   "email",
			Value:   email,
			Message: "不能为空",
		}
	}
	
	return &User{Name: name, Age: age, Email: email}, nil
}

// 演示错误包装
func demonstrateErrorWrapping() {
	fmt.Println("\n--- 错误包装 ---")
	
	// 1. 使用fmt.Errorf包装错误
	originalErr := errors.New("原始错误")
	wrappedErr := fmt.Errorf("包装错误: %w", originalErr)
	
	fmt.Printf("原始错误: %v\n", originalErr)
	fmt.Printf("包装错误: %v\n", wrappedErr)
	
	// 2. 使用errors.Unwrap解包错误
	unwrappedErr := errors.Unwrap(wrappedErr)
	fmt.Printf("解包后的错误: %v\n", unwrappedErr)
	fmt.Printf("解包后是否等于原始错误: %t\n", unwrappedErr == originalErr)
	
	// 3. 使用errors.Is检查错误
	fmt.Printf("包装错误是否包含原始错误: %t\n", errors.Is(wrappedErr, originalErr))
	
	// 4. 多层包装
	doubleWrapped := fmt.Errorf("二次包装: %w", wrappedErr)
	fmt.Printf("二次包装错误: %v\n", doubleWrapped)
	fmt.Printf("二次包装是否包含原始错误: %t\n", errors.Is(doubleWrapped, originalErr))
	
	// 5. 使用errors.As进行类型断言
	valErr := &ValidationError{Field: "test", Value: "invalid", Message: "测试错误"}
	wrappedValErr := fmt.Errorf("包装的验证错误: %w", valErr)
	
	var targetErr *ValidationError
	if errors.As(wrappedValErr, &targetErr) {
		fmt.Printf("成功提取验证错误: 字段=%s, 值=%v\n", targetErr.Field, targetErr.Value)
	}
	
	// 6. 实际应用示例
	err := processData("invalid_data")
	if err != nil {
		fmt.Printf("处理数据错误: %v\n", err)
		
		// 检查是否是验证错误
		var validationErr *ValidationError
		if errors.As(err, &validationErr) {
			fmt.Printf("这是一个验证错误: %s\n", validationErr.Message)
		}
		
		// 检查是否包含特定错误
		if errors.Is(err, ErrInvalidFormat) {
			fmt.Println("数据格式无效")
		}
	}
}

// 预定义的错误
var (
	ErrInvalidFormat = errors.New("数据格式无效")
	ErrDataTooLarge  = errors.New("数据过大")
	ErrPermissionDenied = errors.New("权限不足")
)

// 数据处理函数
func processData(data string) error {
	if err := validateFormat(data); err != nil {
		return fmt.Errorf("数据处理失败: %w", err)
	}
	
	if err := checkSize(data); err != nil {
		return fmt.Errorf("数据处理失败: %w", err)
	}
	
	return nil
}

func validateFormat(data string) error {
	if data == "invalid_data" {
		return fmt.Errorf("格式验证失败: %w", ErrInvalidFormat)
	}
	return nil
}

func checkSize(data string) error {
	if len(data) > 1000 {
		return fmt.Errorf("大小检查失败: %w", ErrDataTooLarge)
	}
	return nil
}

// 演示panic和recover
func demonstratePanicRecover() {
	fmt.Println("\n--- panic和recover ---")
	
	// 1. 基本的panic和recover
	fmt.Println("1. 基本panic和recover:")
	func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("捕获到panic: %v\n", r)
			}
		}()
		
		fmt.Println("即将panic...")
		panic("这是一个测试panic")
		fmt.Println("这行不会执行")
	}()
	
	fmt.Println("程序继续执行")
	
	// 2. 在函数中使用recover
	fmt.Println("\n2. 函数中的panic处理:")
	result := safeFunction()
	fmt.Printf("安全函数返回: %v\n", result)
	
	// 3. 数组越界引发的panic
	fmt.Println("\n3. 数组越界panic:")
	safeArrayAccess()
	
	// 4. nil指针引发的panic
	fmt.Println("\n4. nil指针panic:")
	safePointerAccess()
	
	// 5. 类型断言引发的panic
	fmt.Println("\n5. 类型断言panic:")
	safeTypeAssertion()
	
	// 6. 除零引发的panic
	fmt.Println("\n6. 除零panic:")
	safeDivision()
}

// 安全执行可能panic的函数
func safeFunction() (result string) {
	defer func() {
		if r := recover(); r != nil {
			result = fmt.Sprintf("函数执行失败: %v", r)
		}
	}()
	
	// 模拟可能panic的操作
	arr := []int{1, 2, 3}
	_ = arr[10] // 这会引发panic
	
	return "正常执行完成"
}

// 安全的数组访问
func safeArrayAccess() {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("数组访问panic: %v\n", r)
		}
	}()
	
	arr := [3]int{1, 2, 3}
	fmt.Printf("arr[0] = %d\n", arr[0])
	fmt.Printf("arr[5] = %d\n", arr[5]) // panic: 数组越界
}

// 安全的指针访问
func safePointerAccess() {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("指针访问panic: %v\n", r)
		}
	}()
	
	var ptr *int
	fmt.Printf("*ptr = %d\n", *ptr) // panic: nil指针解引用
}

// 安全的类型断言
func safeTypeAssertion() {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("类型断言panic: %v\n", r)
		}
	}()
	
	var i interface{} = "hello"
	num := i.(int) // panic: 类型断言失败
	fmt.Printf("num = %d\n", num)
}

// 安全的除法
func safeDivision() {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("除法panic: %v\n", r)
		}
	}()
	
	a := 10
	b := 0
	result := a / b // panic: 整数除零
	fmt.Printf("result = %d\n", result)
}

// 演示错误处理模式
func demonstrateErrorPatterns() {
	fmt.Println("\n--- 错误处理模式 ---")
	
	// 1. 早期返回模式
	fmt.Println("1. 早期返回模式:")
	if err := processUserData("", 25, "<EMAIL>"); err != nil {
		fmt.Printf("处理用户数据失败: %v\n", err)
	} else {
		fmt.Println("用户数据处理成功")
	}
	
	// 2. 错误聚合模式
	fmt.Println("\n2. 错误聚合模式:")
	errors := validateUserInput("", -5, "invalid-email")
	if len(errors) > 0 {
		fmt.Println("验证失败:")
		for _, err := range errors {
			fmt.Printf("  - %v\n", err)
		}
	}
	
	// 3. 重试模式
	fmt.Println("\n3. 重试模式:")
	if err := retryOperation(3); err != nil {
		fmt.Printf("重试后仍然失败: %v\n", err)
	} else {
		fmt.Println("操作成功")
	}
	
	// 4. 超时模式
	fmt.Println("\n4. 超时模式:")
	if err := operationWithTimeout(2 * time.Second); err != nil {
		fmt.Printf("操作超时: %v\n", err)
	} else {
		fmt.Println("操作在超时前完成")
	}
}

// 早期返回模式
func processUserData(name string, age int, email string) error {
	if name == "" {
		return errors.New("姓名不能为空")
	}
	
	if age < 0 {
		return errors.New("年龄不能为负数")
	}
	
	if email == "" {
		return errors.New("邮箱不能为空")
	}
	
	// 处理用户数据...
	return nil
}

// 错误聚合模式
func validateUserInput(name string, age int, email string) []error {
	var errors []error
	
	if name == "" {
		errors = append(errors, fmt.Errorf("姓名不能为空"))
	}
	
	if age < 0 || age > 150 {
		errors = append(errors, fmt.Errorf("年龄必须在0-150之间"))
	}
	
	if email == "" || !isValidEmail(email) {
		errors = append(errors, fmt.Errorf("邮箱格式无效"))
	}
	
	return errors
}

func isValidEmail(email string) bool {
	return len(email) > 0 && email != "invalid-email"
}

// 重试模式
func retryOperation(maxRetries int) error {
	var lastErr error
	
	for i := 0; i < maxRetries; i++ {
		if err := unreliableOperation(); err != nil {
			lastErr = err
			fmt.Printf("第%d次尝试失败: %v\n", i+1, err)
			time.Sleep(100 * time.Millisecond) // 等待后重试
			continue
		}
		return nil // 成功
	}
	
	return fmt.Errorf("重试%d次后仍然失败: %w", maxRetries, lastErr)
}

func unreliableOperation() error {
	// 模拟70%的失败率
	if time.Now().UnixNano()%10 < 7 {
		return errors.New("操作失败")
	}
	return nil
}

// 超时模式
func operationWithTimeout(timeout time.Duration) error {
	done := make(chan error, 1)
	
	go func() {
		// 模拟长时间运行的操作
		time.Sleep(3 * time.Second)
		done <- nil
	}()
	
	select {
	case err := <-done:
		return err
	case <-time.After(timeout):
		return fmt.Errorf("操作超时: 超过 %v", timeout)
	}
}

// 演示实用示例
func demonstratePracticalExamples() {
	fmt.Println("\n--- 实用示例 ---")
	
	// 1. 文件操作错误处理
	demonstrateFileOperations()
	
	// 2. 网络请求错误处理
	demonstrateNetworkOperations()
	
	// 3. 数据转换错误处理
	demonstrateDataConversion()
}

func demonstrateFileOperations() {
	fmt.Println("文件操作错误处理:")
	
	// 尝试读取不存在的文件
	content, err := os.ReadFile("nonexistent.txt")
	if err != nil {
		if os.IsNotExist(err) {
			fmt.Println("  文件不存在")
		} else if os.IsPermission(err) {
			fmt.Println("  权限不足")
		} else {
			fmt.Printf("  其他文件错误: %v\n", err)
		}
	} else {
		fmt.Printf("  文件内容: %s\n", content)
	}
	
	// 创建临时文件进行测试
	tmpFile, err := os.CreateTemp("", "test_*.txt")
	if err != nil {
		fmt.Printf("  创建临时文件失败: %v\n", err)
		return
	}
	defer os.Remove(tmpFile.Name()) // 清理
	defer tmpFile.Close()
	
	// 写入文件
	if _, err := tmpFile.WriteString("Hello, World!"); err != nil {
		fmt.Printf("  写入文件失败: %v\n", err)
		return
	}
	
	fmt.Printf("  成功创建并写入临时文件: %s\n", tmpFile.Name())
}

func demonstrateNetworkOperations() {
	fmt.Println("\n网络请求错误处理:")
	
	// 模拟网络请求
	if err := simulateNetworkRequest("https://api.example.com/data"); err != nil {
		if netErr, ok := err.(*NetworkError); ok {
			fmt.Printf("  网络错误: %s\n", netErr.Error())
			if netErr.IsTimeout() {
				fmt.Println("  建议: 增加超时时间或检查网络连接")
			}
		} else {
			fmt.Printf("  其他错误: %v\n", err)
		}
	} else {
		fmt.Println("  网络请求成功")
	}
}

func simulateNetworkRequest(url string) error {
	// 模拟网络超时
	return &NetworkError{
		Operation: "HTTP GET",
		URL:       url,
		Err:       errors.New("连接超时"),
		Timestamp: time.Now(),
	}
}

func demonstrateDataConversion() {
	fmt.Println("\n数据转换错误处理:")
	
	strings := []string{"123", "456.78", "invalid", "789"}
	
	for _, str := range strings {
		if num, err := strconv.Atoi(str); err != nil {
			if numErr, ok := err.(*strconv.NumError); ok {
				fmt.Printf("  转换 '%s' 失败: %s (错误类型: %s)\n", 
					str, numErr.Err, numErr.Func)
			} else {
				fmt.Printf("  转换 '%s' 失败: %v\n", str, err)
			}
		} else {
			fmt.Printf("  转换 '%s' 成功: %d\n", str, num)
		}
	}
}

/*
运行此程序的命令：
go run advanced/02_error_handling.go

学习要点：
1. Go使用显式错误处理，函数通常返回(result, error)
2. error是一个接口，任何实现Error()方法的类型都是错误
3. 自定义错误类型可以携带更多上下文信息
4. 错误包装使用fmt.Errorf和%w动词，可以保持错误链
5. errors.Is和errors.As用于检查和提取包装的错误
6. panic用于不可恢复的错误，recover用于捕获panic
7. 良好的错误处理包括早期返回、错误聚合、重试等模式
8. 标准库提供了许多有用的错误检查函数
*/
