package main

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

/*
Go语言进阶特性 - 反射机制

本文件涵盖：
1. 反射的基本概念
2. Type和Value的使用
3. 结构体字段和方法的反射
4. 反射修改值
5. 反射调用方法
6. 实用示例和最佳实践
*/

func main() {
	fmt.Println("=== Go语言反射机制学习 ===")
	
	// 1. 反射基础
	demonstrateReflectionBasics()
	
	// 2. 类型信息
	demonstrateTypeInfo()
	
	// 3. 值操作
	demonstrateValueOperations()
	
	// 4. 结构体反射
	demonstrateStructReflection()
	
	// 5. 方法反射
	demonstrateMethodReflection()
	
	// 6. 实用示例
	demonstratePracticalExamples()
}

// 演示反射基础
func demonstrateReflectionBasics() {
	fmt.Println("\n--- 反射基础 ---")
	
	// 1. 基本类型反射
	var x float64 = 3.14
	fmt.Printf("变量x: %v\n", x)
	
	// 获取类型信息
	t := reflect.TypeOf(x)
	fmt.Printf("类型: %v\n", t)
	fmt.Printf("类型名称: %v\n", t.Name())
	fmt.Printf("类型种类: %v\n", t.Kind())
	
	// 获取值信息
	v := reflect.ValueOf(x)
	fmt.Printf("值: %v\n", v)
	fmt.Printf("值的类型: %v\n", v.Type())
	fmt.Printf("值的种类: %v\n", v.Kind())
	fmt.Printf("值是否可设置: %v\n", v.CanSet())
	
	// 2. 不同类型的反射
	values := []interface{}{
		42,
		"hello",
		true,
		[]int{1, 2, 3},
		map[string]int{"a": 1},
	}
	
	fmt.Println("\n不同类型的反射:")
	for i, val := range values {
		t := reflect.TypeOf(val)
		v := reflect.ValueOf(val)
		fmt.Printf("  值%d: %v (类型: %v, 种类: %v)\n", i, val, t, v.Kind())
	}
	
	// 3. 接口类型反射
	var i interface{} = "hello world"
	t = reflect.TypeOf(i)
	v = reflect.ValueOf(i)
	fmt.Printf("\n接口类型反射: %v (类型: %v)\n", v, t)
	
	// 4. 指针类型反射
	var ptr *int = &[]int{42}[0]
	t = reflect.TypeOf(ptr)
	v = reflect.ValueOf(ptr)
	fmt.Printf("指针类型反射: %v (类型: %v, 种类: %v)\n", v, t, v.Kind())
	fmt.Printf("指针指向的值: %v\n", v.Elem())
}

// 演示类型信息
func demonstrateTypeInfo() {
	fmt.Println("\n--- 类型信息 ---")
	
	// 1. 切片类型信息
	slice := []string{"a", "b", "c"}
	t := reflect.TypeOf(slice)
	
	fmt.Printf("切片类型: %v\n", t)
	fmt.Printf("元素类型: %v\n", t.Elem())
	fmt.Printf("是否为切片: %v\n", t.Kind() == reflect.Slice)
	
	// 2. 映射类型信息
	m := map[string]int{"key": 42}
	t = reflect.TypeOf(m)
	
	fmt.Printf("映射类型: %v\n", t)
	fmt.Printf("键类型: %v\n", t.Key())
	fmt.Printf("值类型: %v\n", t.Elem())
	
	// 3. 函数类型信息
	fn := func(a int, b string) (bool, error) { return true, nil }
	t = reflect.TypeOf(fn)
	
	fmt.Printf("函数类型: %v\n", t)
	fmt.Printf("参数个数: %d\n", t.NumIn())
	fmt.Printf("返回值个数: %d\n", t.NumOut())
	
	for i := 0; i < t.NumIn(); i++ {
		fmt.Printf("  参数%d类型: %v\n", i, t.In(i))
	}
	
	for i := 0; i < t.NumOut(); i++ {
		fmt.Printf("  返回值%d类型: %v\n", i, t.Out(i))
	}
	
	// 4. 数组类型信息
	arr := [5]int{1, 2, 3, 4, 5}
	t = reflect.TypeOf(arr)
	
	fmt.Printf("数组类型: %v\n", t)
	fmt.Printf("数组长度: %d\n", t.Len())
	fmt.Printf("元素类型: %v\n", t.Elem())
}

// 演示值操作
func demonstrateValueOperations() {
	fmt.Println("\n--- 值操作 ---")
	
	// 1. 基本值操作
	var x int = 42
	v := reflect.ValueOf(&x).Elem() // 获取可设置的值
	
	fmt.Printf("原始值: %d\n", x)
	fmt.Printf("反射值: %v\n", v)
	fmt.Printf("是否可设置: %v\n", v.CanSet())
	
	// 修改值
	v.SetInt(100)
	fmt.Printf("修改后的值: %d\n", x)
	
	// 2. 字符串值操作
	var str string = "hello"
	v = reflect.ValueOf(&str).Elem()
	
	fmt.Printf("原始字符串: %s\n", str)
	v.SetString("world")
	fmt.Printf("修改后的字符串: %s\n", str)
	
	// 3. 切片值操作
	slice := []int{1, 2, 3}
	v = reflect.ValueOf(&slice).Elem()
	
	fmt.Printf("原始切片: %v\n", slice)
	
	// 修改切片元素
	v.Index(0).SetInt(10)
	fmt.Printf("修改元素后: %v\n", slice)
	
	// 追加元素
	newElem := reflect.ValueOf(4)
	v.Set(reflect.Append(v, newElem))
	fmt.Printf("追加元素后: %v\n", slice)
	
	// 4. 映射值操作
	m := map[string]int{"a": 1, "b": 2}
	v = reflect.ValueOf(m)
	
	fmt.Printf("原始映射: %v\n", m)
	
	// 设置映射值
	key := reflect.ValueOf("c")
	val := reflect.ValueOf(3)
	v.SetMapIndex(key, val)
	fmt.Printf("添加元素后: %v\n", m)
	
	// 删除映射元素
	v.SetMapIndex(reflect.ValueOf("a"), reflect.Value{})
	fmt.Printf("删除元素后: %v\n", m)
}

// 定义用于反射的结构体
type Person struct {
	Name    string `json:"name" validate:"required"`
	Age     int    `json:"age" validate:"min=0,max=150"`
	Email   string `json:"email" validate:"email"`
	private string // 私有字段
}

func (p Person) GetInfo() string {
	return fmt.Sprintf("Name: %s, Age: %d", p.Name, p.Age)
}

func (p *Person) SetName(name string) {
	p.Name = name
}

func (p Person) IsAdult() bool {
	return p.Age >= 18
}

// 演示结构体反射
func demonstrateStructReflection() {
	fmt.Println("\n--- 结构体反射 ---")
	
	person := Person{
		Name:    "张三",
		Age:     25,
		Email:   "<EMAIL>",
		private: "私有数据",
	}
	
	t := reflect.TypeOf(person)
	v := reflect.ValueOf(person)
	
	fmt.Printf("结构体类型: %v\n", t)
	fmt.Printf("字段数量: %d\n", t.NumField())
	
	// 1. 遍历结构体字段
	fmt.Println("\n结构体字段:")
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		value := v.Field(i)
		
		fmt.Printf("  字段%d: %s (类型: %v, 值: %v)\n", 
			i, field.Name, field.Type, value)
		
		// 获取标签
		if tag := field.Tag; tag != "" {
			fmt.Printf("    标签: %s\n", tag)
			if jsonTag := tag.Get("json"); jsonTag != "" {
				fmt.Printf("    JSON标签: %s\n", jsonTag)
			}
			if validateTag := tag.Get("validate"); validateTag != "" {
				fmt.Printf("    验证标签: %s\n", validateTag)
			}
		}
		
		// 检查字段是否可导出
		fmt.Printf("    是否可导出: %v\n", field.IsExported())
	}
	
	// 2. 按名称获取字段
	fmt.Println("\n按名称获取字段:")
	if nameField, ok := t.FieldByName("Name"); ok {
		nameValue := v.FieldByName("Name")
		fmt.Printf("Name字段: %v, 值: %v\n", nameField.Type, nameValue)
	}
	
	// 3. 修改结构体字段
	fmt.Println("\n修改结构体字段:")
	personPtr := &person
	v = reflect.ValueOf(personPtr).Elem()
	
	nameField := v.FieldByName("Name")
	if nameField.CanSet() {
		nameField.SetString("李四")
		fmt.Printf("修改后的姓名: %s\n", person.Name)
	}
	
	ageField := v.FieldByName("Age")
	if ageField.CanSet() {
		ageField.SetInt(30)
		fmt.Printf("修改后的年龄: %d\n", person.Age)
	}
	
	// 4. 创建结构体实例
	fmt.Println("\n创建结构体实例:")
	newPersonType := reflect.TypeOf(Person{})
	newPersonValue := reflect.New(newPersonType).Elem()
	
	newPersonValue.FieldByName("Name").SetString("王五")
	newPersonValue.FieldByName("Age").SetInt(35)
	newPersonValue.FieldByName("Email").SetString("<EMAIL>")
	
	newPerson := newPersonValue.Interface().(Person)
	fmt.Printf("新创建的Person: %+v\n", newPerson)
}

// 演示方法反射
func demonstrateMethodReflection() {
	fmt.Println("\n--- 方法反射 ---")
	
	person := Person{Name: "张三", Age: 25, Email: "<EMAIL>"}
	
	t := reflect.TypeOf(person)
	v := reflect.ValueOf(person)
	
	fmt.Printf("方法数量: %d\n", t.NumMethod())
	
	// 1. 遍历方法
	fmt.Println("\n结构体方法:")
	for i := 0; i < t.NumMethod(); i++ {
		method := t.Method(i)
		fmt.Printf("  方法%d: %s (类型: %v)\n", i, method.Name, method.Type)
	}
	
	// 2. 调用无参数方法
	fmt.Println("\n调用方法:")
	getInfoMethod := v.MethodByName("GetInfo")
	if getInfoMethod.IsValid() {
		results := getInfoMethod.Call(nil)
		fmt.Printf("GetInfo结果: %v\n", results[0])
	}
	
	isAdultMethod := v.MethodByName("IsAdult")
	if isAdultMethod.IsValid() {
		results := isAdultMethod.Call(nil)
		fmt.Printf("IsAdult结果: %v\n", results[0])
	}
	
	// 3. 调用有参数的方法（需要指针接收者）
	personPtr := &person
	v = reflect.ValueOf(personPtr)
	
	setNameMethod := v.MethodByName("SetName")
	if setNameMethod.IsValid() {
		args := []reflect.Value{reflect.ValueOf("李四")}
		setNameMethod.Call(args)
		fmt.Printf("SetName后的姓名: %s\n", person.Name)
	}
	
	// 4. 检查方法是否存在
	if method := v.MethodByName("NonExistentMethod"); !method.IsValid() {
		fmt.Println("方法 NonExistentMethod 不存在")
	}
}

// 演示实用示例
func demonstratePracticalExamples() {
	fmt.Println("\n--- 实用示例 ---")
	
	// 1. JSON序列化器
	fmt.Println("1. 简单JSON序列化:")
	person := Person{Name: "张三", Age: 25, Email: "<EMAIL>"}
	jsonStr := simpleJSONMarshal(person)
	fmt.Printf("JSON: %s\n", jsonStr)
	
	// 2. 结构体复制
	fmt.Println("\n2. 结构体复制:")
	original := Person{Name: "原始", Age: 30, Email: "<EMAIL>"}
	copied := copyStruct(original).(Person)
	fmt.Printf("原始: %+v\n", original)
	fmt.Printf("复制: %+v\n", copied)
	
	// 3. 结构体比较
	fmt.Println("\n3. 结构体比较:")
	person1 := Person{Name: "张三", Age: 25, Email: "<EMAIL>"}
	person2 := Person{Name: "张三", Age: 25, Email: "<EMAIL>"}
	person3 := Person{Name: "李四", Age: 30, Email: "<EMAIL>"}
	
	fmt.Printf("person1 == person2: %v\n", deepEqual(person1, person2))
	fmt.Printf("person1 == person3: %v\n", deepEqual(person1, person3))
	
	// 4. 结构体验证
	fmt.Println("\n4. 结构体验证:")
	validPerson := Person{Name: "张三", Age: 25, Email: "<EMAIL>"}
	invalidPerson := Person{Name: "", Age: -5, Email: "invalid"}
	
	fmt.Printf("有效Person验证: %v\n", validateStruct(validPerson))
	fmt.Printf("无效Person验证: %v\n", validateStruct(invalidPerson))
	
	// 5. 动态调用函数
	fmt.Println("\n5. 动态调用函数:")
	calculator := Calculator{}
	result := callMethod(calculator, "Add", 10, 20)
	fmt.Printf("动态调用Add(10, 20): %v\n", result)
	
	result = callMethod(calculator, "Multiply", 5, 6)
	fmt.Printf("动态调用Multiply(5, 6): %v\n", result)
}

// 简单的JSON序列化
func simpleJSONMarshal(v interface{}) string {
	val := reflect.ValueOf(v)
	typ := reflect.TypeOf(v)
	
	if typ.Kind() != reflect.Struct {
		return fmt.Sprintf(`"%v"`, v)
	}
	
	var parts []string
	for i := 0; i < val.NumField(); i++ {
		field := typ.Field(i)
		value := val.Field(i)
		
		if !field.IsExported() {
			continue
		}
		
		jsonTag := field.Tag.Get("json")
		if jsonTag == "" {
			jsonTag = strings.ToLower(field.Name)
		}
		
		var valueStr string
		switch value.Kind() {
		case reflect.String:
			valueStr = fmt.Sprintf(`"%s"`, value.String())
		case reflect.Int, reflect.Int64:
			valueStr = fmt.Sprintf("%d", value.Int())
		default:
			valueStr = fmt.Sprintf(`"%v"`, value.Interface())
		}
		
		parts = append(parts, fmt.Sprintf(`"%s":%s`, jsonTag, valueStr))
	}
	
	return "{" + strings.Join(parts, ",") + "}"
}

// 结构体复制
func copyStruct(src interface{}) interface{} {
	srcVal := reflect.ValueOf(src)
	srcType := reflect.TypeOf(src)
	
	if srcType.Kind() != reflect.Struct {
		return src
	}
	
	dstVal := reflect.New(srcType).Elem()
	
	for i := 0; i < srcVal.NumField(); i++ {
		srcField := srcVal.Field(i)
		dstField := dstVal.Field(i)
		
		if dstField.CanSet() {
			dstField.Set(srcField)
		}
	}
	
	return dstVal.Interface()
}

// 深度比较
func deepEqual(a, b interface{}) bool {
	return reflect.DeepEqual(a, b)
}

// 结构体验证
func validateStruct(v interface{}) []string {
	var errors []string
	
	val := reflect.ValueOf(v)
	typ := reflect.TypeOf(v)
	
	if typ.Kind() != reflect.Struct {
		return []string{"不是结构体类型"}
	}
	
	for i := 0; i < val.NumField(); i++ {
		field := typ.Field(i)
		value := val.Field(i)
		
		validateTag := field.Tag.Get("validate")
		if validateTag == "" {
			continue
		}
		
		rules := strings.Split(validateTag, ",")
		for _, rule := range rules {
			if err := validateField(field.Name, value, rule); err != "" {
				errors = append(errors, err)
			}
		}
	}
	
	return errors
}

// 字段验证
func validateField(fieldName string, value reflect.Value, rule string) string {
	switch rule {
	case "required":
		if value.Kind() == reflect.String && value.String() == "" {
			return fmt.Sprintf("%s 不能为空", fieldName)
		}
	default:
		if strings.HasPrefix(rule, "min=") {
			minStr := strings.TrimPrefix(rule, "min=")
			if min, err := strconv.Atoi(minStr); err == nil {
				if value.Kind() == reflect.Int && value.Int() < int64(min) {
					return fmt.Sprintf("%s 不能小于 %d", fieldName, min)
				}
			}
		}
		if strings.HasPrefix(rule, "max=") {
			maxStr := strings.TrimPrefix(rule, "max=")
			if max, err := strconv.Atoi(maxStr); err == nil {
				if value.Kind() == reflect.Int && value.Int() > int64(max) {
					return fmt.Sprintf("%s 不能大于 %d", fieldName, max)
				}
			}
		}
	}
	return ""
}

// 计算器示例
type Calculator struct{}

func (c Calculator) Add(a, b int) int {
	return a + b
}

func (c Calculator) Multiply(a, b int) int {
	return a * b
}

// 动态调用方法
func callMethod(obj interface{}, methodName string, args ...interface{}) interface{} {
	val := reflect.ValueOf(obj)
	method := val.MethodByName(methodName)
	
	if !method.IsValid() {
		return fmt.Errorf("方法 %s 不存在", methodName)
	}
	
	// 转换参数
	var reflectArgs []reflect.Value
	for _, arg := range args {
		reflectArgs = append(reflectArgs, reflect.ValueOf(arg))
	}
	
	// 调用方法
	results := method.Call(reflectArgs)
	if len(results) > 0 {
		return results[0].Interface()
	}
	
	return nil
}

/*
运行此程序的命令：
go run advanced/04_reflection.go

学习要点：
1. 反射允许程序在运行时检查类型和值的信息
2. reflect.TypeOf()获取类型信息，reflect.ValueOf()获取值信息
3. 通过反射可以动态创建对象、调用方法、修改字段
4. 反射操作比直接操作慢，应谨慎使用
5. 结构体标签可以通过反射读取，常用于序列化和验证
6. 反射常用于框架开发、序列化、ORM等场景
7. 使用反射时要注意类型安全和性能影响
8. reflect.Value的CanSet()方法检查值是否可修改
*/
