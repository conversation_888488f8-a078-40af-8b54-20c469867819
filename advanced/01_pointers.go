package main

import (
	"fmt"
	"unsafe"
)

/*
Go语言进阶特性 - 指针操作

本文件涵盖：
1. 指针的基本概念和语法
2. 指针的创建和使用
3. 指针与函数参数
4. 指针与结构体
5. 指针数组和数组指针
6. 指针的安全性和限制
7. 实用示例和最佳实践
*/

func main() {
	fmt.Println("=== Go语言指针操作学习 ===")
	
	// 1. 指针基础
	demonstratePointerBasics()
	
	// 2. 指针与函数
	demonstratePointersWithFunctions()
	
	// 3. 指针与结构体
	demonstratePointersWithStructs()
	
	// 4. 指针数组和数组指针
	demonstratePointerArrays()
	
	// 5. 指针的高级用法
	demonstrateAdvancedPointers()
	
	// 6. 实用示例
	demonstratePracticalExamples()
}

// 演示指针基础
func demonstratePointerBasics() {
	fmt.Println("\n--- 指针基础 ---")
	
	// 1. 基本变量和指针
	var num int = 42
	var ptr *int = &num // 获取num的地址
	
	fmt.Printf("变量值: %d\n", num)
	fmt.Printf("变量地址: %p\n", &num)
	fmt.Printf("指针值(地址): %p\n", ptr)
	fmt.Printf("指针指向的值: %d\n", *ptr)
	
	// 2. 指针的零值
	var nilPtr *int
	fmt.Printf("nil指针: %v (是否为nil: %t)\n", nilPtr, nilPtr == nil)
	
	// 3. 通过指针修改值
	fmt.Printf("修改前: num=%d\n", num)
	*ptr = 100
	fmt.Printf("修改后: num=%d\n", num)
	
	// 4. 不同类型的指针
	var str string = "Hello"
	var strPtr *string = &str
	fmt.Printf("字符串: %s, 指针: %p, 通过指针访问: %s\n", str, strPtr, *strPtr)
	
	var flag bool = true
	var flagPtr *bool = &flag
	fmt.Printf("布尔值: %t, 指针: %p, 通过指针访问: %t\n", flag, flagPtr, *flagPtr)
	
	// 5. 指针的指针
	var ptrPtr **int = &ptr
	fmt.Printf("指针的指针: %p\n", ptrPtr)
	fmt.Printf("通过指针的指针访问值: %d\n", **ptrPtr)
	
	// 6. new函数创建指针
	newPtr := new(int)
	*newPtr = 200
	fmt.Printf("new创建的指针: %p, 值: %d\n", newPtr, *newPtr)
	
	// 7. 指针比较
	ptr1 := &num
	ptr2 := &num
	ptr3 := new(int)
	*ptr3 = 42
	
	fmt.Printf("ptr1 == ptr2: %t (指向同一个变量)\n", ptr1 == ptr2)
	fmt.Printf("ptr1 == ptr3: %t (指向不同变量)\n", ptr1 == ptr3)
	fmt.Printf("*ptr1 == *ptr3: %t (值相等)\n", *ptr1 == *ptr3)
}

// 演示指针与函数
func demonstratePointersWithFunctions() {
	fmt.Println("\n--- 指针与函数 ---")
	
	// 1. 值传递 vs 指针传递
	num := 10
	fmt.Printf("原始值: %d\n", num)
	
	// 值传递：不会修改原始值
	modifyByValue(num)
	fmt.Printf("值传递后: %d\n", num)
	
	// 指针传递：会修改原始值
	modifyByPointer(&num)
	fmt.Printf("指针传递后: %d\n", num)
	
	// 2. 函数返回指针
	ptr := createPointer(42)
	fmt.Printf("函数返回的指针: %p, 值: %d\n", ptr, *ptr)
	
	// 3. 指针作为函数参数的优势
	largeStruct := LargeStruct{
		Data: [1000]int{},
		Name: "大结构体",
	}
	
	// 填充数据
	for i := 0; i < 1000; i++ {
		largeStruct.Data[i] = i
	}
	
	fmt.Println("处理大结构体:")
	
	// 值传递（复制整个结构体）
	processLargeStructByValue(largeStruct)
	
	// 指针传递（只传递地址）
	processLargeStructByPointer(&largeStruct)
	
	// 4. 交换两个变量的值
	a, b := 10, 20
	fmt.Printf("交换前: a=%d, b=%d\n", a, b)
	swap(&a, &b)
	fmt.Printf("交换后: a=%d, b=%d\n", a, b)
}

// 值传递函数
func modifyByValue(n int) {
	n = 999
	fmt.Printf("函数内修改为: %d\n", n)
}

// 指针传递函数
func modifyByPointer(n *int) {
	*n = 999
	fmt.Printf("函数内通过指针修改为: %d\n", *n)
}

// 返回指针的函数
func createPointer(value int) *int {
	// 局部变量的地址可以安全返回，Go会自动处理内存管理
	local := value
	return &local
}

// 大结构体示例
type LargeStruct struct {
	Data [1000]int
	Name string
}

// 值传递处理大结构体
func processLargeStructByValue(ls LargeStruct) {
	fmt.Printf("  值传递: 结构体名称=%s, 数据大小=%d\n", ls.Name, len(ls.Data))
}

// 指针传递处理大结构体
func processLargeStructByPointer(ls *LargeStruct) {
	fmt.Printf("  指针传递: 结构体名称=%s, 数据大小=%d\n", ls.Name, len(ls.Data))
}

// 交换函数
func swap(a, b *int) {
	*a, *b = *b, *a
}

// 演示指针与结构体
func demonstratePointersWithStructs() {
	fmt.Println("\n--- 指针与结构体 ---")
	
	// 1. 结构体指针
	type Person struct {
		Name string
		Age  int
	}
	
	// 创建结构体
	person1 := Person{Name: "张三", Age: 25}
	fmt.Printf("结构体: %+v\n", person1)
	
	// 获取结构体指针
	personPtr := &person1
	fmt.Printf("结构体指针: %p\n", personPtr)
	
	// 通过指针访问字段
	fmt.Printf("通过指针访问姓名: %s\n", personPtr.Name) // Go自动解引用
	fmt.Printf("通过指针访问年龄: %d\n", (*personPtr).Age) // 显式解引用
	
	// 通过指针修改字段
	personPtr.Age = 26
	fmt.Printf("修改后的结构体: %+v\n", person1)
	
	// 2. 使用new创建结构体指针
	person2 := new(Person)
	person2.Name = "李四"
	person2.Age = 30
	fmt.Printf("new创建的结构体: %+v\n", *person2)
	
	// 3. 结构体指针字面量
	person3 := &Person{
		Name: "王五",
		Age:  35,
	}
	fmt.Printf("指针字面量: %+v\n", *person3)
	
	// 4. 结构体方法的接收者
	person1.UpdateAge(27)
	fmt.Printf("值接收者方法后: %+v\n", person1)
	
	person1.UpdateAgeByPointer(28)
	fmt.Printf("指针接收者方法后: %+v\n", person1)
	
	// 5. 结构体字段指针
	namePtr := &person1.Name
	agePtr := &person1.Age
	
	*namePtr = "张三三"
	*agePtr = 29
	fmt.Printf("通过字段指针修改后: %+v\n", person1)
}

// 为Person定义方法
type Person struct {
	Name string
	Age  int
}

// 值接收者方法
func (p Person) UpdateAge(newAge int) {
	p.Age = newAge // 不会修改原结构体
	fmt.Printf("值接收者内: %+v\n", p)
}

// 指针接收者方法
func (p *Person) UpdateAgeByPointer(newAge int) {
	p.Age = newAge // 会修改原结构体
	fmt.Printf("指针接收者内: %+v\n", *p)
}

// 演示指针数组和数组指针
func demonstratePointerArrays() {
	fmt.Println("\n--- 指针数组和数组指针 ---")
	
	// 1. 指针数组：数组的元素是指针
	var pointerArray [3]*int
	
	a, b, c := 10, 20, 30
	pointerArray[0] = &a
	pointerArray[1] = &b
	pointerArray[2] = &c
	
	fmt.Println("指针数组:")
	for i, ptr := range pointerArray {
		if ptr != nil {
			fmt.Printf("  pointerArray[%d] = %p -> %d\n", i, ptr, *ptr)
		}
	}
	
	// 通过指针数组修改原始值
	*pointerArray[0] = 100
	fmt.Printf("修改后: a=%d\n", a)
	
	// 2. 数组指针：指向数组的指针
	array := [3]int{1, 2, 3}
	arrayPtr := &array
	
	fmt.Printf("原数组: %v\n", array)
	fmt.Printf("数组指针: %p\n", arrayPtr)
	fmt.Printf("通过数组指针访问: %v\n", *arrayPtr)
	
	// 通过数组指针修改数组元素
	arrayPtr[0] = 10 // Go自动解引用
	(*arrayPtr)[1] = 20 // 显式解引用
	fmt.Printf("修改后的数组: %v\n", array)
	
	// 3. 指针切片
	slice := []int{1, 2, 3, 4, 5}
	var pointerSlice []*int
	
	for i := range slice {
		pointerSlice = append(pointerSlice, &slice[i])
	}
	
	fmt.Println("指针切片:")
	for i, ptr := range pointerSlice {
		fmt.Printf("  pointerSlice[%d] = %p -> %d\n", i, ptr, *ptr)
	}
	
	// 4. 切片指针
	slicePtr := &slice
	fmt.Printf("切片指针指向的切片: %v\n", *slicePtr)
	(*slicePtr)[0] = 100
	fmt.Printf("通过切片指针修改后: %v\n", slice)
}

// 演示指针的高级用法
func demonstrateAdvancedPointers() {
	fmt.Println("\n--- 指针的高级用法 ---")
	
	// 1. 指针运算的限制
	fmt.Println("1. Go语言不支持指针运算:")
	arr := [5]int{1, 2, 3, 4, 5}
	ptr := &arr[0]
	fmt.Printf("数组首元素指针: %p -> %d\n", ptr, *ptr)
	
	// ptr++ // 编译错误：Go不支持指针运算
	// ptr = ptr + 1 // 编译错误
	
	// 但可以通过索引访问
	for i := 0; i < len(arr); i++ {
		elemPtr := &arr[i]
		fmt.Printf("arr[%d] 指针: %p -> %d\n", i, elemPtr, *elemPtr)
	}
	
	// 2. unsafe包的使用（谨慎使用）
	fmt.Println("\n2. unsafe包的使用:")
	num := 42
	numPtr := &num
	
	// 获取指针的数值表示
	addr := uintptr(unsafe.Pointer(numPtr))
	fmt.Printf("指针地址的数值: %d (0x%x)\n", addr, addr)
	
	// 计算结构体字段的偏移量
	type TestStruct struct {
		A int32
		B int64
		C string
	}
	
	var ts TestStruct
	fmt.Printf("结构体大小: %d 字节\n", unsafe.Sizeof(ts))
	fmt.Printf("字段A偏移量: %d\n", unsafe.Offsetof(ts.A))
	fmt.Printf("字段B偏移量: %d\n", unsafe.Offsetof(ts.B))
	fmt.Printf("字段C偏移量: %d\n", unsafe.Offsetof(ts.C))
	
	// 3. 函数指针（Go中函数是一等公民）
	fmt.Println("\n3. 函数指针:")
	var funcPtr func(int, int) int
	funcPtr = add
	
	result := funcPtr(10, 20)
	fmt.Printf("通过函数指针调用: %d\n", result)
	
	// 函数指针数组
	operations := []func(int, int) int{add, subtract, multiply}
	operands := []string{"加法", "减法", "乘法"}
	
	for i, op := range operations {
		result := op(10, 5)
		fmt.Printf("%s: 10 和 5 = %d\n", operands[i], result)
	}
}

// 辅助函数
func add(a, b int) int      { return a + b }
func subtract(a, b int) int { return a - b }
func multiply(a, b int) int { return a * b }

// 演示实用示例
func demonstratePracticalExamples() {
	fmt.Println("\n--- 实用示例 ---")
	
	// 1. 链表实现
	demonstrateLinkedList()
	
	// 2. 二叉树实现
	demonstrateBinaryTree()
	
	// 3. 对象池模式
	demonstrateObjectPool()
}

// 链表节点
type ListNode struct {
	Value int
	Next  *ListNode
}

// 链表
type LinkedList struct {
	Head *ListNode
	Size int
}

func (ll *LinkedList) Add(value int) {
	newNode := &ListNode{Value: value}
	if ll.Head == nil {
		ll.Head = newNode
	} else {
		current := ll.Head
		for current.Next != nil {
			current = current.Next
		}
		current.Next = newNode
	}
	ll.Size++
}

func (ll *LinkedList) Print() {
	current := ll.Head
	fmt.Print("链表: ")
	for current != nil {
		fmt.Printf("%d", current.Value)
		if current.Next != nil {
			fmt.Print(" -> ")
		}
		current = current.Next
	}
	fmt.Printf(" (大小: %d)\n", ll.Size)
}

func demonstrateLinkedList() {
	fmt.Println("链表示例:")
	
	list := &LinkedList{}
	list.Add(1)
	list.Add(2)
	list.Add(3)
	list.Add(4)
	
	list.Print()
}

// 二叉树节点
type TreeNode struct {
	Value int
	Left  *TreeNode
	Right *TreeNode
}

// 二叉搜索树
type BinarySearchTree struct {
	Root *TreeNode
}

func (bst *BinarySearchTree) Insert(value int) {
	bst.Root = insertNode(bst.Root, value)
}

func insertNode(node *TreeNode, value int) *TreeNode {
	if node == nil {
		return &TreeNode{Value: value}
	}
	
	if value < node.Value {
		node.Left = insertNode(node.Left, value)
	} else if value > node.Value {
		node.Right = insertNode(node.Right, value)
	}
	
	return node
}

func (bst *BinarySearchTree) InorderTraversal() []int {
	var result []int
	inorder(bst.Root, &result)
	return result
}

func inorder(node *TreeNode, result *[]int) {
	if node != nil {
		inorder(node.Left, result)
		*result = append(*result, node.Value)
		inorder(node.Right, result)
	}
}

func demonstrateBinaryTree() {
	fmt.Println("\n二叉搜索树示例:")
	
	bst := &BinarySearchTree{}
	values := []int{5, 3, 7, 2, 4, 6, 8}
	
	for _, value := range values {
		bst.Insert(value)
	}
	
	fmt.Printf("插入值: %v\n", values)
	fmt.Printf("中序遍历: %v\n", bst.InorderTraversal())
}

// 对象池示例
type Object struct {
	ID   int
	Data string
}

type ObjectPool struct {
	pool []*Object
	size int
}

func NewObjectPool(size int) *ObjectPool {
	pool := make([]*Object, 0, size)
	for i := 0; i < size; i++ {
		pool = append(pool, &Object{ID: i})
	}
	return &ObjectPool{pool: pool, size: size}
}

func (op *ObjectPool) Get() *Object {
	if len(op.pool) == 0 {
		return &Object{ID: -1, Data: "新创建的对象"}
	}
	
	obj := op.pool[len(op.pool)-1]
	op.pool = op.pool[:len(op.pool)-1]
	return obj
}

func (op *ObjectPool) Put(obj *Object) {
	if len(op.pool) < op.size {
		obj.Data = "" // 重置对象状态
		op.pool = append(op.pool, obj)
	}
}

func (op *ObjectPool) Available() int {
	return len(op.pool)
}

func demonstrateObjectPool() {
	fmt.Println("\n对象池示例:")
	
	pool := NewObjectPool(3)
	fmt.Printf("初始可用对象数: %d\n", pool.Available())
	
	// 获取对象
	obj1 := pool.Get()
	obj2 := pool.Get()
	obj1.Data = "对象1的数据"
	obj2.Data = "对象2的数据"
	
	fmt.Printf("获取2个对象后可用数: %d\n", pool.Available())
	fmt.Printf("对象1: ID=%d, Data=%s\n", obj1.ID, obj1.Data)
	fmt.Printf("对象2: ID=%d, Data=%s\n", obj2.ID, obj2.Data)
	
	// 归还对象
	pool.Put(obj1)
	fmt.Printf("归还1个对象后可用数: %d\n", pool.Available())
	
	// 重新获取对象
	obj3 := pool.Get()
	fmt.Printf("重新获取的对象: ID=%d, Data=%s\n", obj3.ID, obj3.Data)
}

/*
运行此程序的命令：
go run advanced/01_pointers.go

学习要点：
1. 指针存储变量的内存地址，通过*操作符解引用
2. &操作符获取变量的地址，*操作符访问指针指向的值
3. 指针的零值是nil，访问nil指针会导致panic
4. 指针传递可以避免大结构体的复制，提高性能
5. Go不支持指针运算，这提高了内存安全性
6. 结构体方法的接收者可以是值或指针，影响是否能修改原对象
7. 指针常用于实现链表、树等数据结构
8. unsafe包提供了底层内存操作，但应谨慎使用
*/
