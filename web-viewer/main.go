package main

import (
	"fmt"
	"html/template"
	"io/fs"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
)

/*
Go语言学习项目 - Web展示应用

这个Web应用提供了一个友好的界面来浏览和学习Go语言项目中的所有代码文件。

功能特性：
1. 文件浏览和导航
2. 代码语法高亮
3. 响应式设计
4. 搜索功能
5. 学习路径指导
*/

// 文件信息结构
type FileInfo struct {
	Name        string `json:"name"`
	Path        string `json:"path"`
	Category    string `json:"category"`
	Description string `json:"description"`
	Content     string `json:"content"`
	Size        int64  `json:"size"`
}

// 分类信息结构
type CategoryInfo struct {
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Files       []FileInfo `json:"files"`
	Order       int        `json:"order"`
}

// 应用结构
type App struct {
	Router     *gin.Engine
	Files      map[string]FileInfo
	Categories map[string]CategoryInfo
}

func main() {
	fmt.Println("=== Go语言学习项目Web展示应用 ===")

	app := NewApp()
	app.LoadFiles()
	app.SetupRoutes()

	fmt.Println("Web服务器启动在: http://localhost:8080")
	fmt.Println("请在浏览器中访问上述地址开始学习Go语言！")

	log.Fatal(app.Router.Run(":8080"))
}

// 创建应用实例
func NewApp() *App {
	gin.SetMode(gin.ReleaseMode)
	
	return &App{
		Router:     gin.Default(),
		Files:      make(map[string]FileInfo),
		Categories: make(map[string]CategoryInfo),
	}
}

// 加载所有Go文件
func (app *App) LoadFiles() {
	// 定义分类信息
	categories := map[string]CategoryInfo{
		"basics": {
			Name:        "基础语法",
			Description: "Go语言的基础语法，包括变量、控制流程、函数等",
			Order:       1,
		},
		"data-structures": {
			Name:        "数据结构",
			Description: "Go语言的数据结构，包括数组、切片、映射、结构体、接口等",
			Order:       2,
		},
		"advanced": {
			Name:        "进阶特性",
			Description: "Go语言的进阶特性，包括指针、并发、反射、泛型等",
			Order:       3,
		},
		"enterprise": {
			Name:        "企业级开发",
			Description: "企业级Go开发，包括Web框架、数据库、微服务、容器化等",
			Order:       4,
		},
	}

	// 文件描述映射
	fileDescriptions := map[string]string{
		"01_variables_types.go":     "变量、数据类型和常量的定义与使用",
		"02_control_flow.go":        "控制流程：if/else、for循环、switch语句",
		"03_functions.go":           "函数定义、参数传递、返回值和闭包",
		"01_arrays_slices.go":       "数组和切片的创建、操作和底层原理",
		"02_maps.go":                "映射（Map）的使用和实用示例",
		"03_structs_methods.go":     "结构体定义、方法和嵌入",
		"04_interfaces.go":          "接口定义、实现和多态性",
		"01_pointers.go":            "指针操作、内存管理和安全性",
		"02_error_handling.go":      "错误处理、自定义错误和panic/recover",
		"03_concurrency.go":         "并发编程：goroutine、channel和同步",
		"04_reflection.go":          "反射机制：类型检查和动态操作",
		"05_generics.go":            "泛型编程：类型参数和约束",
		"06_context.go":             "上下文：超时控制、取消和值传递",
		"07_testing.go":             "测试编写：单元测试、基准测试和覆盖率",
		"07_testing_test.go":        "测试文件：测试用例和测试模式",
		"01_gin_framework.go":       "Gin Web框架：路由、中间件和API开发",
		"02_database_operations.go": "数据库操作：GORM、SQL和事务处理",
		"03_microservices.go":       "微服务架构：服务注册、发现和通信",
		"04_docker_deployment.go":   "Docker容器化：镜像构建和部署",
	}

	// 遍历项目目录
	projectRoot := ".."
	for categoryName, categoryInfo := range categories {
		categoryPath := filepath.Join(projectRoot, categoryName)
		
		if _, err := os.Stat(categoryPath); os.IsNotExist(err) {
			continue
		}

		err := filepath.WalkDir(categoryPath, func(path string, d fs.DirEntry, err error) error {
			if err != nil {
				return err
			}

			if !d.IsDir() && strings.HasSuffix(path, ".go") {
				content, err := os.ReadFile(path)
				if err != nil {
					log.Printf("读取文件失败 %s: %v", path, err)
					return nil
				}

				fileName := d.Name()
				relativePath := strings.TrimPrefix(path, projectRoot+"/")
				
				description := fileDescriptions[fileName]
				if description == "" {
					description = "Go语言学习文件"
				}

				fileInfo := FileInfo{
					Name:        fileName,
					Path:        relativePath,
					Category:    categoryName,
					Description: description,
					Content:     string(content),
					Size:        int64(len(content)),
				}

				app.Files[relativePath] = fileInfo
				
				// 添加到分类
				if category, exists := app.Categories[categoryName]; exists {
					category.Files = append(category.Files, fileInfo)
					app.Categories[categoryName] = category
				} else {
					categoryInfo.Files = []FileInfo{fileInfo}
					app.Categories[categoryName] = categoryInfo
				}
			}

			return nil
		})

		if err != nil {
			log.Printf("遍历目录失败 %s: %v", categoryPath, err)
		}
	}

	// 对每个分类的文件进行排序
	for categoryName, category := range app.Categories {
		sort.Slice(category.Files, func(i, j int) bool {
			return category.Files[i].Name < category.Files[j].Name
		})
		app.Categories[categoryName] = category
	}

	log.Printf("加载了 %d 个文件，%d 个分类", len(app.Files), len(app.Categories))
}

// 设置路由
func (app *App) SetupRoutes() {
	// 加载HTML模板
	app.Router.LoadHTMLGlob("templates/*")
	
	// 静态文件服务
	app.Router.Static("/static", "./static")

	// 主页
	app.Router.GET("/", app.homeHandler)

	// 文件浏览
	app.Router.GET("/file/:category/:filename", app.fileHandler)

	// API路由
	api := app.Router.Group("/api")
	{
		api.GET("/categories", app.categoriesHandler)
		api.GET("/files", app.filesHandler)
		api.GET("/file/:category/:filename", app.fileContentHandler)
		api.GET("/search", app.searchHandler)
	}

	// 学习路径
	app.Router.GET("/learning-path", app.learningPathHandler)

	// 关于页面
	app.Router.GET("/about", app.aboutHandler)
}

// 主页处理
func (app *App) homeHandler(c *gin.Context) {
	// 获取排序后的分类
	var sortedCategories []CategoryInfo
	for _, category := range app.Categories {
		sortedCategories = append(sortedCategories, category)
	}
	
	sort.Slice(sortedCategories, func(i, j int) bool {
		return sortedCategories[i].Order < sortedCategories[j].Order
	})

	c.HTML(http.StatusOK, "index.html", gin.H{
		"title":      "Go语言学习项目",
		"categories": sortedCategories,
		"totalFiles": len(app.Files),
	})
}

// 文件查看处理
func (app *App) fileHandler(c *gin.Context) {
	category := c.Param("category")
	filename := c.Param("filename")
	filePath := category + "/" + filename

	fileInfo, exists := app.Files[filePath]
	if !exists {
		c.HTML(http.StatusNotFound, "error.html", gin.H{
			"title":   "文件未找到",
			"message": "请求的文件不存在",
		})
		return
	}

	// 获取同分类的其他文件
	categoryInfo := app.Categories[category]
	
	c.HTML(http.StatusOK, "file.html", gin.H{
		"title":    fileInfo.Name,
		"file":     fileInfo,
		"category": categoryInfo,
		"content":  template.HTML(app.highlightCode(fileInfo.Content)),
	})
}

// 分类API处理
func (app *App) categoriesHandler(c *gin.Context) {
	var sortedCategories []CategoryInfo
	for _, category := range app.Categories {
		sortedCategories = append(sortedCategories, category)
	}
	
	sort.Slice(sortedCategories, func(i, j int) bool {
		return sortedCategories[i].Order < sortedCategories[j].Order
	})

	c.JSON(http.StatusOK, gin.H{
		"categories": sortedCategories,
		"total":      len(sortedCategories),
	})
}

// 文件列表API处理
func (app *App) filesHandler(c *gin.Context) {
	category := c.Query("category")
	
	var files []FileInfo
	if category != "" {
		if categoryInfo, exists := app.Categories[category]; exists {
			files = categoryInfo.Files
		}
	} else {
		for _, file := range app.Files {
			files = append(files, file)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"files": files,
		"total": len(files),
	})
}

// 文件内容API处理
func (app *App) fileContentHandler(c *gin.Context) {
	category := c.Param("category")
	filename := c.Param("filename")
	filePath := category + "/" + filename

	fileInfo, exists := app.Files[filePath]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "文件不存在",
		})
		return
	}

	c.JSON(http.StatusOK, fileInfo)
}

// 搜索处理
func (app *App) searchHandler(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "搜索关键词不能为空",
		})
		return
	}

	var results []FileInfo
	query = strings.ToLower(query)

	for _, file := range app.Files {
		// 在文件名、描述和内容中搜索
		if strings.Contains(strings.ToLower(file.Name), query) ||
			strings.Contains(strings.ToLower(file.Description), query) ||
			strings.Contains(strings.ToLower(file.Content), query) {
			results = append(results, file)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"results": results,
		"total":   len(results),
		"query":   query,
	})
}

// 学习路径处理
func (app *App) learningPathHandler(c *gin.Context) {
	c.HTML(http.StatusOK, "learning-path.html", gin.H{
		"title": "学习路径",
	})
}

// 关于页面处理
func (app *App) aboutHandler(c *gin.Context) {
	c.HTML(http.StatusOK, "about.html", gin.H{
		"title": "关于项目",
	})
}

// 简单的代码高亮（基础版本）
func (app *App) highlightCode(content string) string {
	// 这里实现简单的语法高亮
	// 在实际项目中，建议使用专业的语法高亮库
	
	// 转义HTML特殊字符
	content = template.HTMLEscapeString(content)
	
	// 简单的关键字高亮
	keywords := []string{
		"package", "import", "func", "var", "const", "type", "struct", "interface",
		"if", "else", "for", "range", "switch", "case", "default", "break", "continue",
		"return", "go", "chan", "select", "defer", "panic", "recover",
		"int", "string", "bool", "float64", "byte", "rune",
	}

	for _, keyword := range keywords {
		content = strings.ReplaceAll(content, keyword+" ", 
			`<span class="keyword">`+keyword+`</span> `)
		content = strings.ReplaceAll(content, keyword+"(", 
			`<span class="keyword">`+keyword+`</span>(`)
	}

	// 字符串高亮
	content = strings.ReplaceAll(content, `"`, `<span class="string">"`)
	
	// 注释高亮
	lines := strings.Split(content, "\n")
	for i, line := range lines {
		if strings.TrimSpace(line) != "" && strings.HasPrefix(strings.TrimSpace(line), "//") {
			lines[i] = `<span class="comment">` + line + `</span>`
		}
		if strings.TrimSpace(line) != "" && strings.HasPrefix(strings.TrimSpace(line), "/*") {
			lines[i] = `<span class="comment">` + line + `</span>`
		}
	}
	
	return strings.Join(lines, "\n")
}

/*
启动说明：

1. 确保在web-viewer目录中运行：
   cd web-viewer
   go run main.go

2. 在浏览器中访问：
   http://localhost:8080

3. 项目结构：
   web-viewer/
   ├── main.go           # 主程序
   ├── static/           # 静态资源（CSS、JS）
   └── templates/        # HTML模板

功能特性：
- 文件浏览和代码查看
- 语法高亮显示
- 分类导航
- 搜索功能
- 响应式设计
- 学习路径指导

技术栈：
- Gin Web框架
- HTML/CSS/JavaScript
- Bootstrap（响应式设计）
- Prism.js（代码高亮）
*/
