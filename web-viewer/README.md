# Go语言学习项目 - Web展示应用

这是一个基于Go语言和Gin框架开发的Web应用，用于展示和浏览Go语言学习项目中的所有代码文件。

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

**Windows用户：**
```bash
# 在项目根目录双击运行
start-web-viewer.bat
```

**Linux/macOS用户：**
```bash
# 在项目根目录运行
chmod +x start-web-viewer.sh
./start-web-viewer.sh
```

### 方法二：手动启动

1. **确保环境要求**
   - Go 1.18 或更高版本
   - 网络连接（首次运行需要下载依赖）

2. **下载依赖**
   ```bash
   go mod tidy
   ```

3. **启动应用**
   ```bash
   cd web-viewer
   go run main.go
   ```

4. **访问应用**
   在浏览器中打开：`http://localhost:8080`

## 📱 功能特性

### 🏠 首页
- 项目概览和统计信息
- 学习模块分类展示
- 快速开始指南
- 项目特色介绍

### 📁 文件浏览
- 按分类浏览所有Go文件
- 语法高亮显示代码
- 代码复制功能
- 文件信息展示
- 文件间导航

### 🔍 搜索功能
- 实时搜索建议
- 全文搜索支持
- 搜索结果高亮
- 快捷键支持（Ctrl/Cmd + K）

### 🗺️ 学习路径
- 系统化的学习计划
- 四个阶段的详细安排
- 项目实践建议
- 学习时间规划

### ℹ️ 关于页面
- 项目详细介绍
- 技术栈说明
- 使用指南
- 贡献方式

## 🎯 使用技巧

### 键盘快捷键
- `Ctrl/Cmd + K`：聚焦搜索框
- `Ctrl/Cmd + C`：复制代码（在代码页面）
- `Ctrl/Cmd + +`：放大代码
- `Ctrl/Cmd + -`：缩小代码
- `Ctrl/Cmd + 0`：重置代码大小
- `Esc`：关闭搜索建议

### 代码操作
- 点击复制按钮一键复制代码
- 使用缩放按钮调整代码大小
- 切换行号显示
- 切换自动换行

### 搜索技巧
- 支持文件名、描述、内容搜索
- 输入2个字符以上显示搜索建议
- 点击搜索结果直接跳转到文件

## 🏗️ 项目结构

```
web-viewer/
├── main.go                    # 主程序入口
├── static/                    # 静态资源
│   ├── css/
│   │   └── style.css         # 自定义样式
│   └── js/
│       └── app.js            # 前端JavaScript
├── templates/                 # HTML模板
│   ├── base.html             # 基础模板
│   ├── index.html            # 首页模板
│   ├── file.html             # 文件查看模板
│   ├── learning-path.html    # 学习路径模板
│   ├── about.html            # 关于页面模板
│   └── error.html            # 错误页面模板
└── README.md                 # 说明文档
```

## 🛠️ 技术栈

### 后端
- **Go 1.18+**：主要编程语言
- **Gin**：Web框架
- **HTML/Template**：模板引擎
- **文件系统**：内容管理

### 前端
- **Bootstrap 5**：UI框架
- **JavaScript ES6+**：交互逻辑
- **Prism.js**：代码语法高亮
- **Font Awesome**：图标库

## 🔧 开发说明

### 添加新的学习文件
1. 在相应的分类目录中添加Go文件
2. 重启Web应用，文件会自动被检测和加载
3. 文件描述会根据文件名自动匹配

### 自定义样式
- 修改 `static/css/style.css` 文件
- 支持响应式设计
- 包含深色主题支持（预留）

### 扩展功能
- 在 `main.go` 中添加新的路由
- 在 `templates/` 中添加新的模板
- 在 `static/js/app.js` 中添加新的JavaScript功能

## 🐛 故障排除

### 常见问题

**1. 端口被占用**
```
Error: listen tcp :8080: bind: address already in use
```
解决方案：
- 关闭占用8080端口的程序
- 或修改 `main.go` 中的端口号

**2. 依赖下载失败**
```
go: module lookup disabled by GOPROXY=off
```
解决方案：
```bash
go env -w GOPROXY=https://goproxy.cn,direct
go mod tidy
```

**3. 模板文件未找到**
```
html/template: pattern matches no files
```
解决方案：
- 确保在 `web-viewer` 目录中运行程序
- 检查 `templates/` 目录是否存在

**4. 静态文件404**
解决方案：
- 确保 `static/` 目录存在
- 检查文件路径是否正确

### 调试模式
在开发时，可以设置环境变量启用调试模式：
```bash
export GIN_MODE=debug
go run main.go
```

## 📝 更新日志

### v1.0.0
- ✅ 基础文件浏览功能
- ✅ 代码语法高亮
- ✅ 搜索功能
- ✅ 响应式设计
- ✅ 学习路径页面
- ✅ 关于页面

### 计划功能
- 🔄 主题切换（深色/浅色）
- 🔄 代码运行功能
- 🔄 学习进度跟踪
- 🔄 代码注释翻译
- 🔄 移动端优化

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**Happy Learning! 🎉**

如果这个项目对你有帮助，请给它一个⭐️！
