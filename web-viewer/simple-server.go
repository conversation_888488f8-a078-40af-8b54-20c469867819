package main

import (
	"fmt"
	"html/template"
	"io/fs"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strings"
)

/*
Go语言学习项目 - 简化版Web展示应用

这是一个不依赖外部包的简化版本，使用Go标准库实现基本的Web服务功能。
当网络环境无法下载Gin等外部依赖时，可以使用这个版本。
*/

// 文件信息结构
type FileInfo struct {
	Name        string `json:"name"`
	Path        string `json:"path"`
	Category    string `json:"category"`
	Description string `json:"description"`
	Content     string `json:"content"`
	Size        int64  `json:"size"`
}

// 分类信息结构
type CategoryInfo struct {
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Files       []FileInfo `json:"files"`
	Order       int        `json:"order"`
}

// 应用结构
type App struct {
	Files      map[string]FileInfo
	Categories map[string]CategoryInfo
}

func main() {
	fmt.Println("=== Go语言学习项目简化版Web展示应用 ===")

	app := NewApp()
	app.LoadFiles()
	app.SetupRoutes()

	fmt.Println("Web服务器启动在: http://localhost:8080")
	fmt.Println("请在浏览器中访问上述地址开始学习Go语言！")
	fmt.Println("按 Ctrl+C 停止服务器")

	log.Fatal(http.ListenAndServe(":8080", nil))
}

// 创建应用实例
func NewApp() *App {
	return &App{
		Files:      make(map[string]FileInfo),
		Categories: make(map[string]CategoryInfo),
	}
}

// 加载所有Go文件
func (app *App) LoadFiles() {
	// 定义分类信息
	categories := map[string]CategoryInfo{
		"basics": {
			Name:        "基础语法",
			Description: "Go语言的基础语法，包括变量、控制流程、函数等",
			Order:       1,
		},
		"data-structures": {
			Name:        "数据结构",
			Description: "Go语言的数据结构，包括数组、切片、映射、结构体、接口等",
			Order:       2,
		},
		"advanced": {
			Name:        "进阶特性",
			Description: "Go语言的进阶特性，包括指针、并发、反射、泛型等",
			Order:       3,
		},
		"enterprise": {
			Name:        "企业级开发",
			Description: "企业级Go开发，包括Web框架、数据库、微服务、容器化等",
			Order:       4,
		},
	}

	// 文件描述映射
	fileDescriptions := map[string]string{
		"01_variables_types.go":     "变量、数据类型和常量的定义与使用",
		"02_control_flow.go":        "控制流程：if/else、for循环、switch语句",
		"03_functions.go":           "函数定义、参数传递、返回值和闭包",
		"01_arrays_slices.go":       "数组和切片的创建、操作和底层原理",
		"02_maps.go":                "映射（Map）的使用和实用示例",
		"03_structs_methods.go":     "结构体定义、方法和嵌入",
		"04_interfaces.go":          "接口定义、实现和多态性",
		"01_pointers.go":            "指针操作、内存管理和安全性",
		"02_error_handling.go":      "错误处理、自定义错误和panic/recover",
		"03_concurrency.go":         "并发编程：goroutine、channel和同步",
		"04_reflection.go":          "反射机制：类型检查和动态操作",
		"05_generics.go":            "泛型编程：类型参数和约束",
		"06_context.go":             "上下文：超时控制、取消和值传递",
		"07_testing.go":             "测试编写：单元测试、基准测试和覆盖率",
		"07_testing_test.go":        "测试文件：测试用例和测试模式",
		"01_gin_framework.go":       "Gin Web框架：路由、中间件和API开发",
		"02_database_operations.go": "数据库操作：GORM、SQL和事务处理",
		"03_microservices.go":       "微服务架构：服务注册、发现和通信",
		"04_docker_deployment.go":   "Docker容器化：镜像构建和部署",
	}

	// 遍历项目目录
	projectRoot := ".."
	for categoryName, categoryInfo := range categories {
		categoryPath := filepath.Join(projectRoot, categoryName)
		
		if _, err := os.Stat(categoryPath); os.IsNotExist(err) {
			continue
		}

		err := filepath.WalkDir(categoryPath, func(path string, d fs.DirEntry, err error) error {
			if err != nil {
				return err
			}

			if !d.IsDir() && strings.HasSuffix(path, ".go") {
				content, err := os.ReadFile(path)
				if err != nil {
					log.Printf("读取文件失败 %s: %v", path, err)
					return nil
				}

				fileName := d.Name()
				relativePath := strings.TrimPrefix(path, projectRoot+"/")
				
				description := fileDescriptions[fileName]
				if description == "" {
					description = "Go语言学习文件"
				}

				fileInfo := FileInfo{
					Name:        fileName,
					Path:        relativePath,
					Category:    categoryName,
					Description: description,
					Content:     string(content),
					Size:        int64(len(content)),
				}

				app.Files[relativePath] = fileInfo
				
				// 添加到分类
				if category, exists := app.Categories[categoryName]; exists {
					category.Files = append(category.Files, fileInfo)
					app.Categories[categoryName] = category
				} else {
					categoryInfo.Files = []FileInfo{fileInfo}
					app.Categories[categoryName] = categoryInfo
				}
			}

			return nil
		})

		if err != nil {
			log.Printf("遍历目录失败 %s: %v", categoryPath, err)
		}
	}

	// 对每个分类的文件进行排序
	for categoryName, category := range app.Categories {
		sort.Slice(category.Files, func(i, j int) bool {
			return category.Files[i].Name < category.Files[j].Name
		})
		app.Categories[categoryName] = category
	}

	log.Printf("加载了 %d 个文件，%d 个分类", len(app.Files), len(app.Categories))
}

// 设置路由
func (app *App) SetupRoutes() {
	// 静态文件服务
	http.Handle("/static/", http.StripPrefix("/static/", http.FileServer(http.Dir("./static/"))))

	// 主页
	http.HandleFunc("/", app.homeHandler)

	// 文件浏览
	http.HandleFunc("/file/", app.fileHandler)

	// API路由
	http.HandleFunc("/api/search", app.searchHandler)
}

// 主页处理
func (app *App) homeHandler(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path != "/" {
		http.NotFound(w, r)
		return
	}

	// 获取排序后的分类
	var sortedCategories []CategoryInfo
	for _, category := range app.Categories {
		sortedCategories = append(sortedCategories, category)
	}
	
	sort.Slice(sortedCategories, func(i, j int) bool {
		return sortedCategories[i].Order < sortedCategories[j].Order
	})

	// 简单的HTML模板
	tmpl := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Go语言学习项目</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 40px; }
        .hero { background: linear-gradient(135deg, #00ADD8 0%, #5DC9E2 100%); color: white; padding: 40px; border-radius: 10px; margin-bottom: 30px; }
        .categories { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .category { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .category h3 { color: #333; margin-top: 0; }
        .file-list { list-style: none; padding: 0; }
        .file-list li { margin: 10px 0; }
        .file-list a { text-decoration: none; color: #007bff; }
        .file-list a:hover { text-decoration: underline; }
        .stats { display: flex; justify-content: center; gap: 40px; margin: 20px 0; }
        .stat { text-align: center; }
        .stat h3 { margin: 0; font-size: 2em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <h1>🐹 Go语言学习项目</h1>
            <p>从基础语法到企业级开发，全面掌握Go语言编程技能</p>
            <div class="stats">
                <div class="stat">
                    <h3>{{.TotalFiles}}</h3>
                    <p>学习文件</p>
                </div>
                <div class="stat">
                    <h3>{{.TotalCategories}}</h3>
                    <p>学习模块</p>
                </div>
            </div>
        </div>
        
        <div class="categories">
            {{range .Categories}}
            <div class="category">
                <h3>{{.Name}}</h3>
                <p>{{.Description}}</p>
                <ul class="file-list">
                    {{range .Files}}
                    <li>
                        <a href="/file/{{.Path}}">{{.Name}}</a>
                        <br><small>{{.Description}}</small>
                    </li>
                    {{end}}
                </ul>
            </div>
            {{end}}
        </div>
    </div>
</body>
</html>
`

	t, err := template.New("home").Parse(tmpl)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	data := struct {
		Categories       []CategoryInfo
		TotalFiles       int
		TotalCategories  int
	}{
		Categories:      sortedCategories,
		TotalFiles:      len(app.Files),
		TotalCategories: len(app.Categories),
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	t.Execute(w, data)
}

// 文件处理
func (app *App) fileHandler(w http.ResponseWriter, r *http.Request) {
	filePath := strings.TrimPrefix(r.URL.Path, "/file/")
	
	fileInfo, exists := app.Files[filePath]
	if !exists {
		http.NotFound(w, r)
		return
	}

	// 简单的文件查看模板
	tmpl := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Name}} - Go语言学习项目</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .code-container { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 10px; overflow-x: auto; }
        .code-container pre { margin: 0; white-space: pre-wrap; font-family: 'Courier New', monospace; line-height: 1.5; }
        .nav { margin-bottom: 20px; }
        .nav a { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin-right: 10px; }
        .nav a:hover { background: #0056b3; }
        .copy-btn { background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-bottom: 10px; }
        .copy-btn:hover { background: #1e7e34; }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav">
            <a href="/">← 返回首页</a>
        </div>
        
        <div class="header">
            <h1>{{.Name}}</h1>
            <p>{{.Description}}</p>
            <p><strong>分类:</strong> {{.Category}} | <strong>大小:</strong> {{.Size}} 字节</p>
        </div>
        
        <button class="copy-btn" onclick="copyCode()">📋 复制代码</button>
        
        <div class="code-container">
            <pre id="code">{{.Content}}</pre>
        </div>
    </div>
    
    <script>
        function copyCode() {
            const code = document.getElementById('code').textContent;
            navigator.clipboard.writeText(code).then(function() {
                alert('代码已复制到剪贴板！');
            }).catch(function(err) {
                console.error('复制失败:', err);
            });
        }
    </script>
</body>
</html>
`

	t, err := template.New("file").Parse(tmpl)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	t.Execute(w, fileInfo)
}

// 搜索处理
func (app *App) searchHandler(w http.ResponseWriter, r *http.Request) {
	query := r.URL.Query().Get("q")
	if query == "" {
		http.Error(w, "搜索关键词不能为空", http.StatusBadRequest)
		return
	}

	var results []FileInfo
	query = strings.ToLower(query)

	for _, file := range app.Files {
		if strings.Contains(strings.ToLower(file.Name), query) ||
			strings.Contains(strings.ToLower(file.Description), query) ||
			strings.Contains(strings.ToLower(file.Content), query) {
			results = append(results, file)
		}
	}

	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	fmt.Fprintf(w, `{"results": %d, "query": "%s"}`, len(results), query)
}

/*
启动说明：

1. 在web-viewer目录中运行：
   go run simple-server.go

2. 在浏览器中访问：
   http://localhost:8080

这个简化版本提供了基本的功能：
- 文件浏览和代码查看
- 简单的搜索功能
- 响应式设计
- 代码复制功能

虽然功能相对简单，但不依赖任何外部包，可以在任何Go环境中运行。
*/
