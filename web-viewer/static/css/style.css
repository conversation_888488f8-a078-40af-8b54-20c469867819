/* Go语言学习项目 - 自定义样式 */

/* 全局样式 */
:root {
    --primary-color: #00ADD8;
    --secondary-color: #5DC9E2;
    --success-color: #00A29C;
    --warning-color: #FDDD00;
    --danger-color: #CE3262;
    --dark-color: #2D2D2D;
    --light-color: #F8F9FA;
    --code-bg: #2D3748;
    --code-text: #E2E8F0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.25rem;
}

.navbar-brand i {
    color: #00ADD8;
}

/* Hero区域样式 */
.hero-section {
    background: linear-gradient(135deg, #00ADD8 0%, #5DC9E2 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 173, 216, 0.3);
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    backdrop-filter: blur(10px);
}

.stat-card h3 {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* 分类卡片样式 */
.category-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.category-card .card-header {
    background: linear-gradient(135deg, #007BFF 0%, #0056B3 100%);
    border-bottom: none;
}

.category-card .list-group-item {
    border: none;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.category-card .list-group-item:hover {
    background-color: #f8f9fa;
}

.category-card .list-group-item:last-child {
    border-bottom: none;
}

.file-name {
    font-family: 'Courier New', monospace;
    font-weight: 500;
    color: #495057;
}

/* 快速开始样式 */
.quick-start-step {
    text-align: center;
    padding: 1.5rem;
    border-radius: 10px;
    background: #f8f9fa;
    height: 100%;
    transition: transform 0.3s ease;
}

.quick-start-step:hover {
    transform: translateY(-3px);
}

.step-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #007BFF 0%, #0056B3 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1rem;
}

/* 特色功能样式 */
.feature-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

/* 代码容器样式 */
.code-container {
    background: var(--code-bg);
    color: var(--code-text);
    border-radius: 8px;
    font-family: 'Fira Code', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    overflow-x: auto;
    max-height: 80vh;
    overflow-y: auto;
}

.code-container code {
    background: transparent;
    color: inherit;
    padding: 1.5rem;
    display: block;
    white-space: pre;
}

/* Prism.js 自定义样式 */
pre[class*="language-"] {
    margin: 0;
    padding: 0;
    background: var(--code-bg);
}

code[class*="language-"] {
    background: transparent;
}

/* 行号样式 */
.line-numbers .line-numbers-rows {
    border-right: 1px solid #4A5568;
    background: #2D3748;
}

.line-numbers .line-numbers-rows > span:before {
    color: #718096;
}

/* 语法高亮自定义颜色 */
.token.comment {
    color: #718096;
}

.token.keyword {
    color: #F56565;
}

.token.string {
    color: #68D391;
}

.token.number {
    color: #F6AD55;
}

.token.function {
    color: #63B3ED;
}

.token.operator {
    color: #ED8936;
}

/* 搜索结果样式 */
.search-result-item {
    transition: background-color 0.2s ease;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

/* 文件信息卡片 */
.card dt {
    font-weight: 600;
    color: #495057;
}

.card dd {
    color: #6c757d;
}

/* 按钮样式增强 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-outline-primary:hover {
    background-color: #007BFF;
    border-color: #007BFF;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 1rem;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .code-container {
        font-size: 12px;
        max-height: 60vh;
    }
    
    .quick-start-step {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-section h1 {
        font-size: 1.75rem;
    }
    
    .category-card {
        margin-bottom: 1rem;
    }
    
    .code-container {
        font-size: 11px;
    }
}

/* 滚动条样式 */
.code-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.code-container::-webkit-scrollbar-track {
    background: #2D3748;
}

.code-container::-webkit-scrollbar-thumb {
    background: #4A5568;
    border-radius: 4px;
}

.code-container::-webkit-scrollbar-thumb:hover {
    background: #718096;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.category-card,
.feature-card,
.quick-start-step {
    animation: fadeIn 0.6s ease-out;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007BFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    border-radius: 6px;
}

/* 列表组样式增强 */
.list-group-item.active {
    background: linear-gradient(135deg, #007BFF 0%, #0056B3 100%);
    border-color: #007BFF;
}

/* 卡片阴影增强 */
.card {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: none;
    border-radius: 12px;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0 !important;
}

/* 表单样式 */
.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: #007BFF;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 页脚样式 */
footer {
    margin-top: auto;
}

footer a {
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #00ADD8 !important;
}

/* 打印样式 */
@media print {
    .navbar,
    .card-header,
    footer,
    .btn,
    .modal {
        display: none !important;
    }
    
    .code-container {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
        border: 1px solid #ccc;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ccc;
    }
}
