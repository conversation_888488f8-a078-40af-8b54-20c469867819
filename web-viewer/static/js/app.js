// Go语言学习项目 - 主要JavaScript功能

// 全局变量
let searchTimeout;
let currentTheme = 'light';

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    // 初始化工具提示
    initializeTooltips();
    
    // 初始化搜索功能
    initializeSearch();
    
    // 初始化主题切换
    initializeTheme();
    
    // 初始化键盘快捷键
    initializeKeyboardShortcuts();
    
    // 初始化滚动效果
    initializeScrollEffects();
    
    // 初始化代码高亮
    initializeCodeHighlighting();
    
    console.log('Go语言学习项目Web应用已初始化');
}

// 初始化工具提示
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 初始化搜索功能
function initializeSearch() {
    const searchForm = document.getElementById('searchForm');
    const searchInput = document.getElementById('searchInput');
    
    if (searchForm && searchInput) {
        // 实时搜索建议
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    showSearchSuggestions(query);
                }, 300);
            } else {
                hideSearchSuggestions();
            }
        });
        
        // 搜索表单提交
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const query = searchInput.value.trim();
            if (query) {
                performSearch(query);
            }
        });
        
        // 点击外部隐藏搜索建议
        document.addEventListener('click', function(e) {
            if (!searchForm.contains(e.target)) {
                hideSearchSuggestions();
            }
        });
    }
}

// 显示搜索建议
function showSearchSuggestions(query) {
    // 创建搜索建议容器
    let suggestionsContainer = document.getElementById('searchSuggestions');
    if (!suggestionsContainer) {
        suggestionsContainer = document.createElement('div');
        suggestionsContainer.id = 'searchSuggestions';
        suggestionsContainer.className = 'search-suggestions position-absolute bg-white border rounded shadow-sm';
        suggestionsContainer.style.cssText = `
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
        `;
        document.getElementById('searchForm').appendChild(suggestionsContainer);
    }
    
    // 获取搜索建议
    fetch(`/api/search?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchSuggestions(data.results.slice(0, 5), suggestionsContainer);
        })
        .catch(error => {
            console.error('获取搜索建议失败:', error);
        });
}

// 显示搜索建议列表
function displaySearchSuggestions(results, container) {
    if (results.length === 0) {
        container.innerHTML = '<div class="p-3 text-muted">没有找到相关结果</div>';
        return;
    }
    
    let html = '';
    results.forEach(file => {
        html += `
            <a href="/file/${file.category}/${file.name}" 
               class="d-block p-2 text-decoration-none text-dark border-bottom suggestion-item">
                <div class="fw-bold">${file.name}</div>
                <small class="text-muted">${file.description}</small>
            </a>
        `;
    });
    
    container.innerHTML = html;
    
    // 添加悬停效果
    container.querySelectorAll('.suggestion-item').forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        item.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
}

// 隐藏搜索建议
function hideSearchSuggestions() {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (suggestionsContainer) {
        suggestionsContainer.remove();
    }
}

// 执行搜索
function performSearch(query) {
    hideSearchSuggestions();
    
    fetch(`/api/search?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data.results, query);
            const modal = new bootstrap.Modal(document.getElementById('searchModal'));
            modal.show();
        })
        .catch(error => {
            console.error('搜索失败:', error);
            showNotification('搜索失败，请稍后重试', 'error');
        });
}

// 显示搜索结果
function displaySearchResults(results, query) {
    const container = document.getElementById('searchResults');
    if (!container) return;
    
    if (results.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5>未找到相关结果</h5>
                <p class="text-muted">尝试使用其他关键词搜索</p>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="mb-3">
            <h6>找到 ${results.length} 个相关结果</h6>
            <small class="text-muted">搜索关键词: "${query}"</small>
        </div>
    `;
    
    results.forEach(file => {
        html += `
            <div class="search-result-item mb-3 p-3 border rounded">
                <h6 class="mb-1">
                    <a href="/file/${file.category}/${file.name}" class="text-decoration-none">
                        <i class="fas fa-file-code me-2"></i>${file.name}
                    </a>
                    <span class="badge bg-secondary ms-2">${getCategoryDisplayName(file.category)}</span>
                </h6>
                <p class="text-muted mb-2">${file.description}</p>
                <small class="text-muted">
                    <i class="fas fa-folder me-1"></i>${file.path}
                    <span class="ms-3">
                        <i class="fas fa-file-alt me-1"></i>${formatFileSize(file.size)}
                    </span>
                </small>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 获取分类显示名称
function getCategoryDisplayName(category) {
    const categoryNames = {
        'basics': '基础语法',
        'data-structures': '数据结构',
        'advanced': '进阶特性',
        'enterprise': '企业级开发'
    };
    return categoryNames[category] || category;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 初始化主题切换
function initializeTheme() {
    // 从localStorage获取保存的主题
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
    
    // 添加主题切换按钮（如果存在）
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }
}

// 设置主题
function setTheme(theme) {
    currentTheme = theme;
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    
    // 更新主题切换按钮图标
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        const icon = themeToggle.querySelector('i');
        if (icon) {
            icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }
    }
}

// 切换主题
function toggleTheme() {
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
}

// 初始化键盘快捷键
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K: 聚焦搜索框
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }
        
        // Escape: 关闭模态框或清除搜索
        if (e.key === 'Escape') {
            const searchInput = document.getElementById('searchInput');
            if (searchInput && searchInput === document.activeElement) {
                searchInput.blur();
                hideSearchSuggestions();
            }
        }
    });
}

// 初始化滚动效果
function initializeScrollEffects() {
    // 平滑滚动到锚点
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // 滚动到顶部按钮
    const scrollToTopBtn = document.getElementById('scrollToTop');
    if (scrollToTopBtn) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                scrollToTopBtn.style.display = 'block';
            } else {
                scrollToTopBtn.style.display = 'none';
            }
        });
        
        scrollToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// 初始化代码高亮
function initializeCodeHighlighting() {
    // 确保Prism.js已加载
    if (typeof Prism !== 'undefined') {
        // 高亮所有代码块
        Prism.highlightAll();
        
        // 添加复制按钮到代码块
        addCopyButtonsToCodeBlocks();
    }
}

// 为代码块添加复制按钮
function addCopyButtonsToCodeBlocks() {
    const codeBlocks = document.querySelectorAll('pre[class*="language-"]');
    
    codeBlocks.forEach(block => {
        // 创建复制按钮
        const copyButton = document.createElement('button');
        copyButton.className = 'btn btn-sm btn-outline-secondary copy-code-btn';
        copyButton.innerHTML = '<i class="fas fa-copy"></i>';
        copyButton.title = '复制代码';
        
        // 设置按钮样式
        copyButton.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        `;
        
        // 设置容器为相对定位
        block.style.position = 'relative';
        
        // 添加复制功能
        copyButton.addEventListener('click', function() {
            const code = block.querySelector('code');
            const text = code.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                showNotification('代码已复制到剪贴板', 'success');
                
                // 临时改变按钮状态
                const originalHTML = copyButton.innerHTML;
                copyButton.innerHTML = '<i class="fas fa-check"></i>';
                copyButton.classList.remove('btn-outline-secondary');
                copyButton.classList.add('btn-success');
                
                setTimeout(() => {
                    copyButton.innerHTML = originalHTML;
                    copyButton.classList.remove('btn-success');
                    copyButton.classList.add('btn-outline-secondary');
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
                showNotification('复制失败', 'error');
            });
        });
        
        block.appendChild(copyButton);
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show notification`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
    `;
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // 自动移除通知
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 导出全局函数供HTML模板使用
window.GoLearningApp = {
    showNotification,
    performSearch,
    toggleTheme,
    formatFileSize,
    getCategoryDisplayName
};
