{{template "base.html" .}}

{{define "content"}}
<div class="row">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="text-center mb-5">
            <h1 class="display-4 mb-3">
                <i class="fas fa-info-circle me-3 text-primary"></i>
                关于项目
            </h1>
            <p class="lead text-muted">
                了解Go语言学习项目的设计理念和使用方法
            </p>
        </div>

        <!-- 项目介绍 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fab fa-golang me-2"></i>项目介绍
                </h3>
            </div>
            <div class="card-body">
                <p class="lead">
                    这是一个全面的Go语言学习项目，旨在帮助开发者从零基础到企业级开发，
                    系统性地掌握Go语言编程技能。
                </p>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h5><i class="fas fa-target me-2 text-primary"></i>项目目标</h5>
                        <ul>
                            <li>提供系统化的Go语言学习路径</li>
                            <li>通过实际代码示例加深理解</li>
                            <li>涵盖从基础到企业级开发的全部内容</li>
                            <li>培养良好的编程习惯和最佳实践</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-users me-2 text-success"></i>适用人群</h5>
                        <ul>
                            <li>Go语言初学者</li>
                            <li>有其他编程语言基础的开发者</li>
                            <li>希望系统学习Go语言的程序员</li>
                            <li>准备从事Go语言开发的工程师</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目特色 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-star me-2"></i>项目特色
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="feature-highlight text-center">
                            <i class="fas fa-code fa-3x text-primary mb-3"></i>
                            <h5>丰富的代码示例</h5>
                            <p class="text-muted">
                                每个概念都配有详细的代码示例和注释说明，
                                帮助理解和掌握Go语言的各种特性。
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="feature-highlight text-center">
                            <i class="fas fa-route fa-3x text-success mb-3"></i>
                            <h5>系统的学习路径</h5>
                            <p class="text-muted">
                                从基础语法到企业级开发，循序渐进的学习安排，
                                确保知识体系的完整性和连贯性。
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="feature-highlight text-center">
                            <i class="fas fa-laptop-code fa-3x text-info mb-3"></i>
                            <h5>实践导向</h5>
                            <p class="text-muted">
                                强调动手实践，每个阶段都有相应的项目建议，
                                理论与实践相结合。
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="feature-highlight text-center">
                            <i class="fas fa-mobile-alt fa-3x text-warning mb-3"></i>
                            <h5>响应式设计</h5>
                            <p class="text-muted">
                                支持手机、平板、电脑等各种设备，
                                随时随地都能学习Go语言。
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="feature-highlight text-center">
                            <i class="fas fa-search fa-3x text-danger mb-3"></i>
                            <h5>强大的搜索</h5>
                            <p class="text-muted">
                                快速搜索功能，帮助你快速找到需要的
                                代码示例和学习内容。
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="feature-highlight text-center">
                            <i class="fas fa-copy fa-3x text-secondary mb-3"></i>
                            <h5>便捷的代码复制</h5>
                            <p class="text-muted">
                                一键复制代码到剪贴板，方便在本地
                                环境中运行和实验。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术栈 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>技术栈
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fas fa-server me-2 text-primary"></i>后端技术</h5>
                        <div class="tech-stack">
                            <span class="badge bg-primary me-2 mb-2">Go 1.18+</span>
                            <span class="badge bg-success me-2 mb-2">Gin Web框架</span>
                            <span class="badge bg-info me-2 mb-2">HTML模板</span>
                            <span class="badge bg-warning me-2 mb-2">文件系统</span>
                        </div>
                        <p class="mt-3 text-muted">
                            使用Go语言和Gin框架构建Web服务器，
                            提供文件浏览、搜索和代码展示功能。
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-palette me-2 text-success"></i>前端技术</h5>
                        <div class="tech-stack">
                            <span class="badge bg-primary me-2 mb-2">Bootstrap 5</span>
                            <span class="badge bg-success me-2 mb-2">JavaScript ES6+</span>
                            <span class="badge bg-info me-2 mb-2">Prism.js</span>
                            <span class="badge bg-warning me-2 mb-2">Font Awesome</span>
                        </div>
                        <p class="mt-3 text-muted">
                            使用现代前端技术栈，提供美观的界面
                            和良好的用户体验。
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目结构 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-folder-tree me-2"></i>项目结构
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>学习内容目录</h5>
                        <pre class="bg-light p-3 rounded"><code>GolangStudy/
├── basics/              # 基础语法
│   ├── 01_variables_types.go
│   ├── 02_control_flow.go
│   └── 03_functions.go
├── data-structures/     # 数据结构
│   ├── 01_arrays_slices.go
│   ├── 02_maps.go
│   ├── 03_structs_methods.go
│   └── 04_interfaces.go
├── advanced/            # 进阶特性
│   ├── 01_pointers.go
│   ├── 02_error_handling.go
│   ├── 03_concurrency.go
│   ├── 04_reflection.go
│   ├── 05_generics.go
│   ├── 06_context.go
│   └── 07_testing.go
└── enterprise/          # 企业级开发
    ├── 01_gin_framework.go
    ├── 02_database_operations.go
    ├── 03_microservices.go
    └── 04_docker_deployment.go</code></pre>
                    </div>
                    <div class="col-md-6">
                        <h5>Web应用目录</h5>
                        <pre class="bg-light p-3 rounded"><code>web-viewer/
├── main.go              # 主程序
├── static/              # 静态资源
│   ├── css/
│   │   └── style.css
│   └── js/
│       └── app.js
├── templates/           # HTML模板
│   ├── base.html
│   ├── index.html
│   ├── file.html
│   ├── learning-path.html
│   └── about.html
└── README.md           # 说明文档</code></pre>
                        
                        <div class="mt-3">
                            <h6>主要功能模块</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-home me-2 text-primary"></i>首页展示</li>
                                <li><i class="fas fa-file-code me-2 text-success"></i>代码浏览</li>
                                <li><i class="fas fa-search me-2 text-info"></i>搜索功能</li>
                                <li><i class="fas fa-route me-2 text-warning"></i>学习路径</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>使用说明
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fas fa-play me-2 text-primary"></i>快速开始</h5>
                        <ol>
                            <li>确保安装了Go 1.18或更高版本</li>
                            <li>克隆或下载项目到本地</li>
                            <li>在web-viewer目录中运行：<code>go run main.go</code></li>
                            <li>在浏览器中访问：<code>http://localhost:8080</code></li>
                        </ol>
                        
                        <div class="alert alert-info mt-3">
                            <strong>提示：</strong>首次运行时，Go会自动下载所需的依赖包。
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-keyboard me-2 text-success"></i>快捷键</h5>
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <td><kbd>Ctrl/Cmd + K</kbd></td>
                                    <td>聚焦搜索框</td>
                                </tr>
                                <tr>
                                    <td><kbd>Ctrl/Cmd + C</kbd></td>
                                    <td>复制代码（在代码页面）</td>
                                </tr>
                                <tr>
                                    <td><kbd>Ctrl/Cmd + +</kbd></td>
                                    <td>放大代码</td>
                                </tr>
                                <tr>
                                    <td><kbd>Ctrl/Cmd + -</kbd></td>
                                    <td>缩小代码</td>
                                </tr>
                                <tr>
                                    <td><kbd>Ctrl/Cmd + 0</kbd></td>
                                    <td>重置代码大小</td>
                                </tr>
                                <tr>
                                    <td><kbd>Esc</kbd></td>
                                    <td>关闭搜索建议</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 贡献指南 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-hands-helping me-2"></i>贡献指南
                </h3>
            </div>
            <div class="card-body">
                <p>我们欢迎任何形式的贡献，包括但不限于：</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-code me-2 text-primary"></i>代码贡献</h6>
                        <ul>
                            <li>修复bug和改进功能</li>
                            <li>添加新的学习示例</li>
                            <li>优化代码结构和性能</li>
                            <li>改进用户界面和体验</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-edit me-2 text-success"></i>文档贡献</h6>
                        <ul>
                            <li>完善代码注释和说明</li>
                            <li>翻译文档到其他语言</li>
                            <li>编写使用教程和指南</li>
                            <li>分享学习心得和经验</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-success mt-3">
                    <h6 class="alert-heading">
                        <i class="fas fa-heart me-2"></i>如何参与
                    </h6>
                    <ol class="mb-0">
                        <li>Fork项目到你的GitHub账户</li>
                        <li>创建新的功能分支</li>
                        <li>提交你的修改</li>
                        <li>发起Pull Request</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- 联系方式 -->
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>联系我们
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-3">
                        <i class="fab fa-github fa-3x text-dark mb-2"></i>
                        <h6>GitHub</h6>
                        <p class="text-muted">查看源代码和提交Issue</p>
                        <a href="#" class="btn btn-outline-dark">访问GitHub</a>
                    </div>
                    <div class="col-md-4 text-center mb-3">
                        <i class="fas fa-comments fa-3x text-primary mb-2"></i>
                        <h6>讨论区</h6>
                        <p class="text-muted">参与技术讨论和交流</p>
                        <a href="#" class="btn btn-outline-primary">加入讨论</a>
                    </div>
                    <div class="col-md-4 text-center mb-3">
                        <i class="fas fa-envelope fa-3x text-success mb-2"></i>
                        <h6>邮件联系</h6>
                        <p class="text-muted">发送反馈和建议</p>
                        <a href="mailto:<EMAIL>" class="btn btn-outline-success">发送邮件</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<style>
.feature-highlight {
    padding: 2rem 1rem;
    border-radius: 10px;
    background: #f8f9fa;
    height: 100%;
    transition: transform 0.3s ease;
}

.feature-highlight:hover {
    transform: translateY(-5px);
}

.tech-stack .badge {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

pre code {
    font-size: 0.85rem;
    line-height: 1.4;
}

.table kbd {
    background-color: #6c757d;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
}
</style>
{{end}}
