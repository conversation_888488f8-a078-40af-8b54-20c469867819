{{template "base.html" .}}

{{define "content"}}
<div class="row">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="text-center mb-5">
            <h1 class="display-4 mb-3">
                <i class="fas fa-route me-3 text-primary"></i>
                Go语言学习路径
            </h1>
            <p class="lead text-muted">
                系统化的学习计划，助你从零基础到Go语言专家
            </p>
        </div>

        <!-- 学习路径时间线 -->
        <div class="learning-timeline">
            <!-- 第一阶段：基础语法 -->
            <div class="timeline-item">
                <div class="timeline-marker bg-primary">
                    <i class="fas fa-code text-white"></i>
                </div>
                <div class="timeline-content">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">
                                第一阶段：基础语法 (1-2周)
                            </h4>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                掌握Go语言的基础语法，包括变量、数据类型、控制流程和函数等核心概念。
                            </p>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-variable me-2"></i>变量和类型</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/basics/01_variables_types.go">基本数据类型</a></li>
                                            <li>变量声明和初始化</li>
                                            <li>常量定义</li>
                                            <li>类型转换</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-code-branch me-2"></i>控制流程</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/basics/02_control_flow.go">if/else条件语句</a></li>
                                            <li>for循环的各种形式</li>
                                            <li>switch语句</li>
                                            <li>break和continue</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-function me-2"></i>函数</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/basics/03_functions.go">函数定义和调用</a></li>
                                            <li>参数传递</li>
                                            <li>多返回值</li>
                                            <li>匿名函数和闭包</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <strong>学习目标：</strong>能够编写简单的Go程序，理解基本语法结构
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第二阶段：数据结构 -->
            <div class="timeline-item">
                <div class="timeline-marker bg-success">
                    <i class="fas fa-database text-white"></i>
                </div>
                <div class="timeline-content">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0">
                                第二阶段：数据结构 (2-3周)
                            </h4>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                深入学习Go语言的数据结构，包括数组、切片、映射、结构体和接口。
                            </p>
                            
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-list me-2"></i>数组和切片</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/data-structures/01_arrays_slices.go">数组的定义和使用</a></li>
                                            <li>切片的动态特性</li>
                                            <li>切片的底层原理</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-map me-2"></i>映射</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/data-structures/02_maps.go">Map的创建和操作</a></li>
                                            <li>遍历和查找</li>
                                            <li>实用示例</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-cube me-2"></i>结构体</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/data-structures/03_structs_methods.go">结构体定义</a></li>
                                            <li>方法和接收者</li>
                                            <li>结构体嵌入</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-plug me-2"></i>接口</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/data-structures/04_interfaces.go">接口定义和实现</a></li>
                                            <li>空接口和类型断言</li>
                                            <li>接口的多态性</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-success">
                                <strong>学习目标：</strong>熟练使用Go的数据结构，能够设计合理的数据模型
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第三阶段：进阶特性 -->
            <div class="timeline-item">
                <div class="timeline-marker bg-warning">
                    <i class="fas fa-rocket text-white"></i>
                </div>
                <div class="timeline-content">
                    <div class="card">
                        <div class="card-header bg-warning text-white">
                            <h4 class="mb-0">
                                第三阶段：进阶特性 (3-4周)
                            </h4>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                学习Go语言的进阶特性，包括指针、并发编程、反射、泛型等高级概念。
                            </p>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-location-arrow me-2"></i>指针和内存</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/advanced/01_pointers.go">指针的基本概念</a></li>
                                            <li>指针与函数、结构体</li>
                                            <li>内存管理</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-exclamation-triangle me-2"></i>错误处理</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/advanced/02_error_handling.go">error接口</a></li>
                                            <li>自定义错误类型</li>
                                            <li>panic和recover</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-sync me-2"></i>并发编程</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/advanced/03_concurrency.go">goroutine和channel</a></li>
                                            <li>select语句</li>
                                            <li>同步原语</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-mirror me-2"></i>反射</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/advanced/04_reflection.go">反射的基本概念</a></li>
                                            <li>类型和值的操作</li>
                                            <li>实用示例</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-shapes me-2"></i>泛型</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/advanced/05_generics.go">泛型函数和类型</a></li>
                                            <li>类型约束</li>
                                            <li>实用示例</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-sitemap me-2"></i>上下文和测试</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/advanced/06_context.go">Context的使用</a></li>
                                            <li><a href="/file/advanced/07_testing.go">单元测试编写</a></li>
                                            <li>基准测试</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-warning">
                                <strong>学习目标：</strong>掌握Go的高级特性，能够编写高质量的并发程序
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第四阶段：企业级开发 -->
            <div class="timeline-item">
                <div class="timeline-marker bg-danger">
                    <i class="fas fa-building text-white"></i>
                </div>
                <div class="timeline-content">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h4 class="mb-0">
                                第四阶段：企业级开发 (4-6周)
                            </h4>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                学习企业级Go开发，包括Web框架、数据库操作、微服务架构和容器化部署。
                            </p>
                            
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-globe me-2"></i>Web开发</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/enterprise/01_gin_framework.go">Gin框架</a></li>
                                            <li>路由和中间件</li>
                                            <li>RESTful API设计</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-database me-2"></i>数据库</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/enterprise/02_database_operations.go">GORM ORM框架</a></li>
                                            <li>原生SQL操作</li>
                                            <li>数据库设计</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-microchip me-2"></i>微服务</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/enterprise/03_microservices.go">微服务架构</a></li>
                                            <li>服务注册与发现</li>
                                            <li>服务间通信</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="learning-module">
                                        <h6><i class="fas fa-docker me-2"></i>容器化</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="/file/enterprise/04_docker_deployment.go">Docker容器化</a></li>
                                            <li>多阶段构建</li>
                                            <li>容器编排</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-danger">
                                <strong>学习目标：</strong>能够独立开发和部署企业级Go应用程序
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习建议 -->
        <div class="card mt-5">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>学习建议
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fas fa-clock me-2 text-primary"></i>时间安排</h5>
                        <ul>
                            <li>每天坚持学习1-2小时</li>
                            <li>理论学习与实践编程相结合</li>
                            <li>每周完成一个小项目</li>
                            <li>定期复习已学内容</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-tools me-2 text-success"></i>实践方法</h5>
                        <ul>
                            <li>运行所有示例代码</li>
                            <li>修改参数观察不同结果</li>
                            <li>尝试解决实际问题</li>
                            <li>参与开源项目</li>
                        </ul>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h5><i class="fas fa-users me-2 text-info"></i>社区参与</h5>
                        <ul>
                            <li>加入Go语言社区</li>
                            <li>参加技术分享会</li>
                            <li>在论坛提问和回答</li>
                            <li>关注Go语言官方动态</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-book me-2 text-warning"></i>扩展阅读</h5>
                        <ul>
                            <li>《Go语言圣经》</li>
                            <li>《Go语言实战》</li>
                            <li>Go官方文档</li>
                            <li>优秀的Go开源项目</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目实践建议 -->
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-project-diagram me-2"></i>项目实践建议
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="project-suggestion">
                            <h6 class="text-primary">基础阶段项目</h6>
                            <ul class="list-unstyled">
                                <li>• 计算器程序</li>
                                <li>• 猜数字游戏</li>
                                <li>• 文件操作工具</li>
                                <li>• 简单的命令行工具</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="project-suggestion">
                            <h6 class="text-success">数据结构阶段项目</h6>
                            <ul class="list-unstyled">
                                <li>• 通讯录管理系统</li>
                                <li>• 学生成绩管理</li>
                                <li>• 简单的数据分析工具</li>
                                <li>• JSON/CSV处理器</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="project-suggestion">
                            <h6 class="text-warning">进阶阶段项目</h6>
                            <ul class="list-unstyled">
                                <li>• 并发下载器</li>
                                <li>• 简单的Web爬虫</li>
                                <li>• 聊天室应用</li>
                                <li>• 任务调度器</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="project-suggestion">
                            <h6 class="text-danger">企业级项目</h6>
                            <ul class="list-unstyled">
                                <li>• RESTful API服务</li>
                                <li>• 博客系统</li>
                                <li>• 电商后端</li>
                                <li>• 微服务架构项目</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<style>
.learning-timeline {
    position: relative;
    padding-left: 30px;
}

.learning-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #28a745, #ffc107, #dc3545);
}

.timeline-item {
    position: relative;
    margin-bottom: 3rem;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 20px;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.timeline-content {
    margin-left: 40px;
}

.learning-module {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    height: 100%;
}

.learning-module h6 {
    color: #495057;
    margin-bottom: 0.75rem;
}

.learning-module ul li {
    margin-bottom: 0.25rem;
}

.learning-module a {
    color: #007bff;
    text-decoration: none;
}

.learning-module a:hover {
    text-decoration: underline;
}

.project-suggestion {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid;
    height: 100%;
}

.project-suggestion:nth-child(1) { border-left-color: #007bff; }
.project-suggestion:nth-child(2) { border-left-color: #28a745; }
.project-suggestion:nth-child(3) { border-left-color: #ffc107; }
.project-suggestion:nth-child(4) { border-left-color: #dc3545; }
</style>
{{end}}
