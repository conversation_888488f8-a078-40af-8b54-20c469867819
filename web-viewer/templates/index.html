{{template "base.html" .}}

{{define "content"}}
<div class="row">
    <!-- Hero Section -->
    <div class="col-12 mb-5">
        <div class="hero-section bg-gradient text-white p-5 rounded">
            <div class="container text-center">
                <h1 class="display-4 mb-3">
                    <i class="fab fa-golang me-3"></i>
                    Go语言学习项目
                </h1>
                <p class="lead mb-4">
                    从基础语法到企业级开发，全面掌握Go语言编程技能
                </p>
                <div class="row justify-content-center">
                    <div class="col-md-3 col-6 mb-3">
                        <div class="stat-card">
                            <h3 class="h2">{{.totalFiles}}</h3>
                            <p class="mb-0">学习文件</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <div class="stat-card">
                            <h3 class="h2">{{len .categories}}</h3>
                            <p class="mb-0">学习模块</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <div class="stat-card">
                            <h3 class="h2">4</h3>
                            <p class="mb-0">学习阶段</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <div class="stat-card">
                            <h3 class="h2">∞</h3>
                            <p class="mb-0">实践机会</p>
                        </div>
                    </div>
                </div>
                <a href="/learning-path" class="btn btn-light btn-lg mt-3">
                    <i class="fas fa-route me-2"></i>开始学习之旅
                </a>
            </div>
        </div>
    </div>

    <!-- Learning Categories -->
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-book me-2"></i>学习模块
        </h2>
        
        <div class="row">
            {{range .categories}}
            <div class="col-lg-6 col-xl-3 mb-4">
                <div class="card category-card h-100 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            {{if eq .Name "基础语法"}}
                                <i class="fas fa-code me-2"></i>
                            {{else if eq .Name "数据结构"}}
                                <i class="fas fa-database me-2"></i>
                            {{else if eq .Name "进阶特性"}}
                                <i class="fas fa-rocket me-2"></i>
                            {{else if eq .Name "企业级开发"}}
                                <i class="fas fa-building me-2"></i>
                            {{else}}
                                <i class="fas fa-folder me-2"></i>
                            {{end}}
                            {{.Name}}
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">{{.Description}}</p>
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-file-code me-1"></i>
                                {{len .Files}} 个文件
                            </small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="list-group list-group-flush">
                            {{range .Files}}
                            <a href="/file/{{.Category}}/{{.Name}}" 
                               class="list-group-item list-group-item-action py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="file-name">{{.Name}}</span>
                                    <i class="fas fa-chevron-right text-muted"></i>
                                </div>
                                <small class="text-muted">{{.Description}}</small>
                            </a>
                            {{end}}
                        </div>
                    </div>
                </div>
            </div>
            {{end}}
        </div>
    </div>

    <!-- Quick Start Guide -->
    <div class="col-12 mt-5">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-play-circle me-2"></i>快速开始
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="quick-start-step">
                            <div class="step-number">1</div>
                            <h5>选择学习模块</h5>
                            <p>根据你的水平选择合适的学习模块，建议从基础语法开始。</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="quick-start-step">
                            <div class="step-number">2</div>
                            <h5>阅读代码示例</h5>
                            <p>仔细阅读代码注释和示例，理解每个概念的用法。</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="quick-start-step">
                            <div class="step-number">3</div>
                            <h5>动手实践</h5>
                            <p>复制代码到本地运行，修改参数观察不同的结果。</p>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <h6 class="alert-heading">
                        <i class="fas fa-lightbulb me-2"></i>学习建议
                    </h6>
                    <ul class="mb-0">
                        <li>每天坚持学习1-2小时，保持连续性</li>
                        <li>理论学习与实践编程相结合</li>
                        <li>遇到问题时多查阅官方文档</li>
                        <li>加入Go语言社区，与其他学习者交流</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Features -->
    <div class="col-12 mt-5">
        <h3 class="mb-4">
            <i class="fas fa-star me-2"></i>项目特色
        </h3>
        <div class="row">
            <div class="col-md-3 mb-3">
                <div class="feature-card text-center p-4">
                    <i class="fas fa-code fa-3x text-primary mb-3"></i>
                    <h5>丰富的代码示例</h5>
                    <p class="text-muted">每个概念都有详细的代码示例和注释说明</p>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="feature-card text-center p-4">
                    <i class="fas fa-route fa-3x text-success mb-3"></i>
                    <h5>系统的学习路径</h5>
                    <p class="text-muted">从基础到高级，循序渐进的学习安排</p>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="feature-card text-center p-4">
                    <i class="fas fa-mobile-alt fa-3x text-info mb-3"></i>
                    <h5>响应式设计</h5>
                    <p class="text-muted">支持手机、平板、电脑等各种设备</p>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="feature-card text-center p-4">
                    <i class="fas fa-search fa-3x text-warning mb-3"></i>
                    <h5>强大的搜索</h5>
                    <p class="text-muted">快速找到你需要的代码和概念</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Results Modal -->
<div class="modal fade" id="searchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-search me-2"></i>搜索结果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="searchResults">
                    <!-- Search results will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script>
// Search functionality
document.getElementById('searchForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const query = document.getElementById('searchInput').value.trim();
    if (query) {
        searchFiles(query);
    }
});

function searchFiles(query) {
    fetch(`/api/search?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data.results, query);
            const modal = new bootstrap.Modal(document.getElementById('searchModal'));
            modal.show();
        })
        .catch(error => {
            console.error('搜索失败:', error);
            alert('搜索失败，请稍后重试');
        });
}

function displaySearchResults(results, query) {
    const container = document.getElementById('searchResults');
    
    if (results.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5>未找到相关结果</h5>
                <p class="text-muted">尝试使用其他关键词搜索</p>
            </div>
        `;
        return;
    }
    
    let html = `<p class="text-muted mb-3">找到 ${results.length} 个相关结果</p>`;
    
    results.forEach(file => {
        html += `
            <div class="search-result-item mb-3 p-3 border rounded">
                <h6 class="mb-1">
                    <a href="/file/${file.category}/${file.name}" class="text-decoration-none">
                        ${file.name}
                    </a>
                    <span class="badge bg-secondary ms-2">${file.category}</span>
                </h6>
                <p class="text-muted mb-0">${file.description}</p>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Add smooth scrolling
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth'
            });
        }
    });
});
</script>
{{end}}
