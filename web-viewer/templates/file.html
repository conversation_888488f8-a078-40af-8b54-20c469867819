{{template "base.html" .}}

{{define "content"}}
<div class="row">
    <!-- Sidebar -->
    <div class="col-lg-3 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-folder me-2"></i>{{.category.Name}}
                </h6>
            </div>
            <div class="list-group list-group-flush">
                {{range .category.Files}}
                <a href="/file/{{.Category}}/{{.Name}}" 
                   class="list-group-item list-group-item-action {{if eq .Name $.file.Name}}active{{end}}">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="file-name">{{.Name}}</span>
                        {{if eq .Name $.file.Name}}
                        <i class="fas fa-eye text-white"></i>
                        {{else}}
                        <i class="fas fa-chevron-right text-muted"></i>
                        {{end}}
                    </div>
                    <small class="{{if eq .Name $.file.Name}}text-white-50{{else}}text-muted{{end}}">
                        {{.Description}}
                    </small>
                </a>
                {{end}}
            </div>
        </div>

        <!-- File Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>文件信息
                </h6>
            </div>
            <div class="card-body">
                <dl class="row mb-0">
                    <dt class="col-sm-4">文件名:</dt>
                    <dd class="col-sm-8">{{.file.Name}}</dd>
                    
                    <dt class="col-sm-4">分类:</dt>
                    <dd class="col-sm-8">{{.category.Name}}</dd>
                    
                    <dt class="col-sm-4">大小:</dt>
                    <dd class="col-sm-8">{{.file.Size}} 字节</dd>
                    
                    <dt class="col-sm-4">路径:</dt>
                    <dd class="col-sm-8"><code>{{.file.Path}}</code></dd>
                </dl>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tools me-2"></i>快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="copyCode()">
                        <i class="fas fa-copy me-1"></i>复制代码
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="downloadFile()">
                        <i class="fas fa-download me-1"></i>下载文件
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="toggleLineNumbers()">
                        <i class="fas fa-list-ol me-1"></i>行号显示
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="toggleWordWrap()">
                        <i class="fas fa-text-width me-1"></i>自动换行
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-lg-9">
        <!-- File Header -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h4 class="mb-2">
                            <i class="fas fa-file-code me-2 text-primary"></i>
                            {{.file.Name}}
                        </h4>
                        <p class="text-muted mb-0">{{.file.Description}}</p>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-primary">{{.category.Name}}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Code Content -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-code me-2"></i>源代码
                </h6>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-secondary" onclick="zoomOut()">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetZoom()">
                        100%
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="zoomIn()">
                        <i class="fas fa-search-plus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <pre class="code-container mb-0" id="codeContainer"><code class="language-go line-numbers">{{.file.Content}}</code></pre>
            </div>
        </div>

        <!-- Navigation -->
        <div class="row mt-4">
            <div class="col-6">
                {{$prevFile := ""}}
                {{$found := false}}
                {{range .category.Files}}
                    {{if and (not $found) (ne .Name $.file.Name)}}
                        {{$prevFile = .Name}}
                    {{end}}
                    {{if eq .Name $.file.Name}}
                        {{$found = true}}
                    {{end}}
                {{end}}
                
                {{if $prevFile}}
                <a href="/file/{{.file.Category}}/{{$prevFile}}" class="btn btn-outline-primary">
                    <i class="fas fa-chevron-left me-2"></i>上一个文件
                </a>
                {{end}}
            </div>
            <div class="col-6 text-end">
                {{$nextFile := ""}}
                {{$found := false}}
                {{range .category.Files}}
                    {{if $found}}
                        {{$nextFile = .Name}}
                        {{break}}
                    {{end}}
                    {{if eq .Name $.file.Name}}
                        {{$found = true}}
                    {{end}}
                {{end}}
                
                {{if $nextFile}}
                <a href="/file/{{.file.Category}}/{{$nextFile}}" class="btn btn-outline-primary">
                    下一个文件<i class="fas fa-chevron-right ms-2"></i>
                </a>
                {{end}}
            </div>
        </div>

        <!-- Run Instructions -->
        <div class="alert alert-info mt-4">
            <h6 class="alert-heading">
                <i class="fas fa-play me-2"></i>运行说明
            </h6>
            <p class="mb-2">要运行这个文件，请在终端中执行以下命令：</p>
            <code class="d-block bg-dark text-light p-2 rounded">
                go run {{.file.Path}}
            </code>
            <hr>
            <p class="mb-0">
                <strong>提示：</strong>确保你已经安装了Go语言环境，并且在项目根目录中运行命令。
            </p>
        </div>
    </div>
</div>

<!-- Copy Success Toast -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="copyToast" class="toast" role="alert">
        <div class="toast-header">
            <i class="fas fa-check-circle text-success me-2"></i>
            <strong class="me-auto">成功</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            代码已复制到剪贴板！
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script>
let currentZoom = 100;
let lineNumbersVisible = true;
let wordWrapEnabled = false;

// Copy code to clipboard
function copyCode() {
    const codeElement = document.querySelector('#codeContainer code');
    const text = codeElement.textContent;
    
    navigator.clipboard.writeText(text).then(function() {
        showToast();
    }).catch(function(err) {
        console.error('复制失败:', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast();
    });
}

// Show success toast
function showToast() {
    const toast = new bootstrap.Toast(document.getElementById('copyToast'));
    toast.show();
}

// Download file
function downloadFile() {
    const content = document.querySelector('#codeContainer code').textContent;
    const filename = '{{.file.Name}}';
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// Toggle line numbers
function toggleLineNumbers() {
    const codeElement = document.querySelector('#codeContainer code');
    lineNumbersVisible = !lineNumbersVisible;
    
    if (lineNumbersVisible) {
        codeElement.classList.add('line-numbers');
    } else {
        codeElement.classList.remove('line-numbers');
    }
    
    // Re-highlight code
    Prism.highlightElement(codeElement);
}

// Toggle word wrap
function toggleWordWrap() {
    const preElement = document.querySelector('#codeContainer');
    wordWrapEnabled = !wordWrapEnabled;
    
    if (wordWrapEnabled) {
        preElement.style.whiteSpace = 'pre-wrap';
        preElement.style.wordBreak = 'break-word';
    } else {
        preElement.style.whiteSpace = 'pre';
        preElement.style.wordBreak = 'normal';
    }
}

// Zoom functions
function zoomIn() {
    currentZoom = Math.min(currentZoom + 10, 200);
    updateZoom();
}

function zoomOut() {
    currentZoom = Math.max(currentZoom - 10, 50);
    updateZoom();
}

function resetZoom() {
    currentZoom = 100;
    updateZoom();
}

function updateZoom() {
    const codeContainer = document.getElementById('codeContainer');
    codeContainer.style.fontSize = currentZoom + '%';
    
    // Update zoom display
    const zoomButton = document.querySelector('.btn-group .btn:nth-child(2)');
    zoomButton.textContent = currentZoom + '%';
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case 'c':
                if (window.getSelection().toString() === '') {
                    e.preventDefault();
                    copyCode();
                }
                break;
            case '=':
            case '+':
                e.preventDefault();
                zoomIn();
                break;
            case '-':
                e.preventDefault();
                zoomOut();
                break;
            case '0':
                e.preventDefault();
                resetZoom();
                break;
        }
    }
});

// Initialize Prism highlighting
document.addEventListener('DOMContentLoaded', function() {
    Prism.highlightAll();
});
</script>
{{end}}
