{{template "base.html" .}}

{{define "content"}}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="text-center">
            <!-- 错误图标 -->
            <div class="error-icon mb-4">
                <i class="fas fa-exclamation-triangle fa-5x text-warning"></i>
            </div>
            
            <!-- 错误标题 -->
            <h1 class="display-4 mb-3">出错了！</h1>
            
            <!-- 错误消息 -->
            <div class="alert alert-warning" role="alert">
                <h5 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i>错误信息
                </h5>
                <p class="mb-0">{{.message}}</p>
            </div>
            
            <!-- 建议操作 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-lightbulb me-2"></i>建议操作
                    </h6>
                    <ul class="list-unstyled text-start">
                        <li class="mb-2">
                            <i class="fas fa-arrow-left me-2 text-primary"></i>
                            <a href="javascript:history.back()" class="text-decoration-none">返回上一页</a>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-home me-2 text-success"></i>
                            <a href="/" class="text-decoration-none">回到首页</a>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-search me-2 text-info"></i>
                            <a href="#" onclick="focusSearch()" class="text-decoration-none">搜索内容</a>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-route me-2 text-warning"></i>
                            <a href="/learning-path" class="text-decoration-none">查看学习路径</a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 快速导航 -->
            <div class="row mt-4">
                <div class="col-6 col-md-3 mb-2">
                    <a href="/file/basics/01_variables_types.go" class="btn btn-outline-primary btn-sm w-100">
                        基础语法
                    </a>
                </div>
                <div class="col-6 col-md-3 mb-2">
                    <a href="/file/data-structures/01_arrays_slices.go" class="btn btn-outline-success btn-sm w-100">
                        数据结构
                    </a>
                </div>
                <div class="col-6 col-md-3 mb-2">
                    <a href="/file/advanced/01_pointers.go" class="btn btn-outline-warning btn-sm w-100">
                        进阶特性
                    </a>
                </div>
                <div class="col-6 col-md-3 mb-2">
                    <a href="/file/enterprise/01_gin_framework.go" class="btn btn-outline-danger btn-sm w-100">
                        企业开发
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script>
function focusSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.focus();
        searchInput.select();
    }
}
</script>

<style>
.error-icon {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}
</style>
{{end}}
