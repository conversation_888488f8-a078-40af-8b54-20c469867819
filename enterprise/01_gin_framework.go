package main

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

/*
Go语言企业级开发 - Gin框架介绍和示例

本文件涵盖：
1. Gin框架基础
2. 路由定义
3. 中间件使用
4. 请求处理
5. 响应格式
6. 错误处理
7. 实用示例
*/

// 用户模型
type User struct {
	ID       int    `json:"id"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	Age      int    `json:"age"`
	CreateAt string `json:"create_at"`
}

// 响应模型
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// 模拟数据库
var users = []User{
	{ID: 1, Name: "张三", Email: "<EMAIL>", Age: 25, CreateAt: "2023-01-01"},
	{ID: 2, Name: "李四", Email: "<EMAIL>", Age: 30, CreateAt: "2023-01-02"},
	{ID: 3, Name: "王五", Email: "<EMAIL>", Age: 28, CreateAt: "2023-01-03"},
}

var nextID = 4

func main() {
	// 创建Gin实例
	r := gin.Default()

	// 设置信任的代理
	r.SetTrustedProxies([]string{"127.0.0.1"})

	// 全局中间件
	r.Use(LoggerMiddleware())
	r.Use(CORSMiddleware())
	r.Use(gin.Recovery())

	// 静态文件服务
	r.Static("/static", "./static")
	r.StaticFile("/favicon.ico", "./static/favicon.ico")

	// HTML模板
	r.LoadHTMLGlob("templates/*")

	// 基础路由
	r.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "Gin框架示例",
			"users": users,
		})
	})

	r.GET("/ping", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "pong",
			"time":    time.Now().Format("2006-01-02 15:04:05"),
		})
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		// 用户相关路由
		userRoutes := api.Group("/users")
		{
			userRoutes.GET("", getUsers)
			userRoutes.GET("/:id", getUser)
			userRoutes.POST("", createUser)
			userRoutes.PUT("/:id", updateUser)
			userRoutes.DELETE("/:id", deleteUser)
		}

		// 认证相关路由
		auth := api.Group("/auth")
		{
			auth.POST("/login", login)
			auth.POST("/logout", AuthMiddleware(), logout)
			auth.GET("/profile", AuthMiddleware(), getProfile)
		}

		// 受保护的路由
		protected := api.Group("/protected")
		protected.Use(AuthMiddleware())
		{
			protected.GET("/data", getProtectedData)
			protected.POST("/upload", uploadFile)
		}
	}

	// 健康检查
	r.GET("/health", healthCheck)

	// 启动服务器
	r.Run(":8080")
}

// ============ 中间件 ============

// 日志中间件
func LoggerMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
			param.ClientIP,
			param.TimeStamp.Format("2006/01/02 - 15:04:05"),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	})
}

// CORS中间件
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// 认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, Response{
				Code:    401,
				Message: "未提供认证令牌",
			})
			c.Abort()
			return
		}

		// 简单的令牌验证（实际应用中应该验证JWT等）
		if token != "Bearer valid-token" {
			c.JSON(http.StatusUnauthorized, Response{
				Code:    401,
				Message: "无效的认证令牌",
			})
			c.Abort()
			return
		}

		// 设置用户信息到上下文
		c.Set("userID", 1)
		c.Set("username", "admin")
		c.Next()
	}
}

// ============ 处理函数 ============

// 获取所有用户
func getUsers(c *gin.Context) {
	// 查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	name := c.Query("name")

	// 过滤用户
	var filteredUsers []User
	for _, user := range users {
		if name == "" || user.Name == name {
			filteredUsers = append(filteredUsers, user)
		}
	}

	// 分页
	start := (page - 1) * limit
	end := start + limit
	if start > len(filteredUsers) {
		start = len(filteredUsers)
	}
	if end > len(filteredUsers) {
		end = len(filteredUsers)
	}

	result := filteredUsers[start:end]

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "获取用户列表成功",
		Data: gin.H{
			"users": result,
			"total": len(filteredUsers),
			"page":  page,
			"limit": limit,
		},
	})
}

// 获取单个用户
func getUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的用户ID",
		})
		return
	}

	for _, user := range users {
		if user.ID == id {
			c.JSON(http.StatusOK, Response{
				Code:    200,
				Message: "获取用户成功",
				Data:    user,
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, Response{
		Code:    404,
		Message: "用户不存在",
	})
}

// 创建用户
func createUser(c *gin.Context) {
	var newUser User
	if err := c.ShouldBindJSON(&newUser); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "请求数据格式错误: " + err.Error(),
		})
		return
	}

	// 验证数据
	if newUser.Name == "" {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "用户名不能为空",
		})
		return
	}

	if newUser.Email == "" {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "邮箱不能为空",
		})
		return
	}

	// 检查邮箱是否已存在
	for _, user := range users {
		if user.Email == newUser.Email {
			c.JSON(http.StatusConflict, Response{
				Code:    409,
				Message: "邮箱已存在",
			})
			return
		}
	}

	// 创建用户
	newUser.ID = nextID
	nextID++
	newUser.CreateAt = time.Now().Format("2006-01-02")
	users = append(users, newUser)

	c.JSON(http.StatusCreated, Response{
		Code:    201,
		Message: "用户创建成功",
		Data:    newUser,
	})
}

// 更新用户
func updateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的用户ID",
		})
		return
	}

	var updateData User
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "请求数据格式错误: " + err.Error(),
		})
		return
	}

	// 查找并更新用户
	for i, user := range users {
		if user.ID == id {
			if updateData.Name != "" {
				users[i].Name = updateData.Name
			}
			if updateData.Email != "" {
				users[i].Email = updateData.Email
			}
			if updateData.Age > 0 {
				users[i].Age = updateData.Age
			}

			c.JSON(http.StatusOK, Response{
				Code:    200,
				Message: "用户更新成功",
				Data:    users[i],
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, Response{
		Code:    404,
		Message: "用户不存在",
	})
}

// 删除用户
func deleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的用户ID",
		})
		return
	}

	// 查找并删除用户
	for i, user := range users {
		if user.ID == id {
			users = append(users[:i], users[i+1:]...)
			c.JSON(http.StatusOK, Response{
				Code:    200,
				Message: "用户删除成功",
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, Response{
		Code:    404,
		Message: "用户不存在",
	})
}

// 登录
func login(c *gin.Context) {
	var loginData struct {
		Email    string `json:"email" binding:"required"`
		Password string `json:"password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&loginData); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "请求数据格式错误: " + err.Error(),
		})
		return
	}

	// 简单的登录验证
	if loginData.Email == "<EMAIL>" && loginData.Password == "password" {
		c.JSON(http.StatusOK, Response{
			Code:    200,
			Message: "登录成功",
			Data: gin.H{
				"token":    "valid-token",
				"userID":   1,
				"username": "admin",
			},
		})
		return
	}

	c.JSON(http.StatusUnauthorized, Response{
		Code:    401,
		Message: "邮箱或密码错误",
	})
}

// 登出
func logout(c *gin.Context) {
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "登出成功",
	})
}

// 获取用户资料
func getProfile(c *gin.Context) {
	userID, _ := c.Get("userID")
	username, _ := c.Get("username")

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "获取用户资料成功",
		Data: gin.H{
			"userID":   userID,
			"username": username,
		},
	})
}

// 获取受保护的数据
func getProtectedData(c *gin.Context) {
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "获取受保护数据成功",
		Data: gin.H{
			"secret": "这是受保护的数据",
			"time":   time.Now().Format("2006-01-02 15:04:05"),
		},
	})
}

// 文件上传
func uploadFile(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "文件上传失败: " + err.Error(),
		})
		return
	}

	// 保存文件
	filename := "./uploads/" + file.Filename
	if err := c.SaveUploadedFile(file, filename); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "文件保存失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "文件上传成功",
		Data: gin.H{
			"filename": file.Filename,
			"size":     file.Size,
		},
	})
}

// 健康检查
func healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
		"version":   "1.0.0",
	})
}

/*
运行此程序的命令：
go run enterprise/01_gin_framework.go

API测试示例：
1. 获取所有用户: GET http://localhost:8080/api/v1/users
2. 获取单个用户: GET http://localhost:8080/api/v1/users/1
3. 创建用户: POST http://localhost:8080/api/v1/users
4. 登录: POST http://localhost:8080/api/v1/auth/login
5. 健康检查: GET http://localhost:8080/health

学习要点：
1. Gin是高性能的HTTP Web框架
2. 支持中间件、路由组、参数绑定等特性
3. 内置JSON、XML、HTML等多种响应格式
4. 支持文件上传、静态文件服务
5. 提供丰富的中间件生态系统
6. 适合构建RESTful API和微服务
*/
