package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

/*
Go语言企业级开发 - Docker容器化部署

本文件涵盖：
1. Docker容器化应用
2. 多阶段构建
3. 环境变量配置
4. 健康检查
5. 容器编排
6. 生产环境部署
*/

// 配置结构
type Config struct {
	Port        int    `json:"port"`
	Environment string `json:"environment"`
	DBHost      string `json:"db_host"`
	DBPort      int    `json:"db_port"`
	DBName      string `json:"db_name"`
	RedisHost   string `json:"redis_host"`
	RedisPort   int    `json:"redis_port"`
	LogLevel    string `json:"log_level"`
}

// 应用结构
type App struct {
	Config *Config
	Router *gin.Engine
}

// 响应结构
type Response struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp string      `json:"timestamp"`
}

// 健康检查响应
type HealthResponse struct {
	Status      string            `json:"status"`
	Version     string            `json:"version"`
	Environment string            `json:"environment"`
	Uptime      string            `json:"uptime"`
	Checks      map[string]string `json:"checks"`
}

var startTime = time.Now()

func main() {
	fmt.Println("=== Go语言Docker容器化部署学习 ===")

	// 加载配置
	config := loadConfig()

	// 创建应用
	app := NewApp(config)

	// 启动应用
	app.Start()
}

// 加载配置
func loadConfig() *Config {
	config := &Config{
		Port:        8080,
		Environment: "development",
		DBHost:      "localhost",
		DBPort:      5432,
		DBName:      "myapp",
		RedisHost:   "localhost",
		RedisPort:   6379,
		LogLevel:    "info",
	}

	// 从环境变量读取配置
	if port := os.Getenv("PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Port = p
		}
	}

	if env := os.Getenv("ENVIRONMENT"); env != "" {
		config.Environment = env
	}

	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		config.DBHost = dbHost
	}

	if dbPort := os.Getenv("DB_PORT"); dbPort != "" {
		if p, err := strconv.Atoi(dbPort); err == nil {
			config.DBPort = p
		}
	}

	if dbName := os.Getenv("DB_NAME"); dbName != "" {
		config.DBName = dbName
	}

	if redisHost := os.Getenv("REDIS_HOST"); redisHost != "" {
		config.RedisHost = redisHost
	}

	if redisPort := os.Getenv("REDIS_PORT"); redisPort != "" {
		if p, err := strconv.Atoi(redisPort); err == nil {
			config.RedisPort = p
		}
	}

	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		config.LogLevel = logLevel
	}

	return config
}

// 创建应用
func NewApp(config *Config) *App {
	// 设置Gin模式
	if config.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	app := &App{
		Config: config,
		Router: gin.Default(),
	}

	// 设置路由
	app.setupRoutes()

	return app
}

// 设置路由
func (app *App) setupRoutes() {
	// 中间件
	app.Router.Use(app.loggingMiddleware())
	app.Router.Use(app.corsMiddleware())

	// 基础路由
	app.Router.GET("/", app.homeHandler)
	app.Router.GET("/health", app.healthHandler)
	app.Router.GET("/ready", app.readinessHandler)
	app.Router.GET("/config", app.configHandler)

	// API路由组
	api := app.Router.Group("/api/v1")
	{
		api.GET("/status", app.statusHandler)
		api.GET("/info", app.infoHandler)
		api.POST("/echo", app.echoHandler)
	}

	// 管理路由组
	admin := app.Router.Group("/admin")
	{
		admin.GET("/metrics", app.metricsHandler)
		admin.GET("/debug", app.debugHandler)
	}
}

// 启动应用
func (app *App) Start() {
	log.Printf("应用启动在端口 %d，环境: %s", app.Config.Port, app.Config.Environment)
	log.Printf("数据库: %s:%d/%s", app.Config.DBHost, app.Config.DBPort, app.Config.DBName)
	log.Printf("Redis: %s:%d", app.Config.RedisHost, app.Config.RedisPort)

	addr := fmt.Sprintf(":%d", app.Config.Port)
	if err := app.Router.Run(addr); err != nil {
		log.Fatal("启动服务器失败:", err)
	}
}

// ============ 中间件 ============

// 日志中间件
func (app *App) loggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] %s %s %d %s %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.ClientIP,
		)
	})
}

// CORS中间件
func (app *App) corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// ============ 处理函数 ============

// 首页处理
func (app *App) homeHandler(c *gin.Context) {
	c.JSON(http.StatusOK, Response{
		Code:      200,
		Message:   "欢迎使用Docker化的Go应用",
		Timestamp: time.Now().Format(time.RFC3339),
		Data: gin.H{
			"version":     "1.0.0",
			"environment": app.Config.Environment,
			"uptime":      time.Since(startTime).String(),
		},
	})
}

// 健康检查处理
func (app *App) healthHandler(c *gin.Context) {
	checks := make(map[string]string)

	// 检查数据库连接
	checks["database"] = app.checkDatabase()

	// 检查Redis连接
	checks["redis"] = app.checkRedis()

	// 检查磁盘空间
	checks["disk"] = app.checkDisk()

	// 检查内存使用
	checks["memory"] = app.checkMemory()

	// 确定整体状态
	status := "healthy"
	for _, check := range checks {
		if check != "ok" {
			status = "unhealthy"
			break
		}
	}

	statusCode := http.StatusOK
	if status == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, HealthResponse{
		Status:      status,
		Version:     "1.0.0",
		Environment: app.Config.Environment,
		Uptime:      time.Since(startTime).String(),
		Checks:      checks,
	})
}

// 就绪检查处理
func (app *App) readinessHandler(c *gin.Context) {
	// 简单的就绪检查
	ready := true
	message := "应用已就绪"

	// 检查关键依赖
	if app.checkDatabase() != "ok" {
		ready = false
		message = "数据库未就绪"
	}

	statusCode := http.StatusOK
	if !ready {
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, gin.H{
		"ready":   ready,
		"message": message,
	})
}

// 配置信息处理
func (app *App) configHandler(c *gin.Context) {
	// 隐藏敏感信息
	safeConfig := map[string]interface{}{
		"port":        app.Config.Port,
		"environment": app.Config.Environment,
		"db_host":     app.Config.DBHost,
		"db_port":     app.Config.DBPort,
		"db_name":     app.Config.DBName,
		"redis_host":  app.Config.RedisHost,
		"redis_port":  app.Config.RedisPort,
		"log_level":   app.Config.LogLevel,
	}

	c.JSON(http.StatusOK, Response{
		Code:      200,
		Message:   "应用配置信息",
		Data:      safeConfig,
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// 状态处理
func (app *App) statusHandler(c *gin.Context) {
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "服务运行正常",
		Data: gin.H{
			"status":      "running",
			"environment": app.Config.Environment,
			"uptime":      time.Since(startTime).String(),
			"timestamp":   time.Now().Format(time.RFC3339),
		},
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// 信息处理
func (app *App) infoHandler(c *gin.Context) {
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "应用信息",
		Data: gin.H{
			"name":        "docker-go-app",
			"version":     "1.0.0",
			"description": "Docker化的Go应用示例",
			"author":      "Go学习项目",
			"build_time":  "2024-01-01T00:00:00Z",
			"go_version":  "1.21",
		},
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// 回声处理
func (app *App) echoHandler(c *gin.Context) {
	var request map[string]interface{}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:      400,
			Message:   "请求格式错误: " + err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "回声响应",
		Data: gin.H{
			"received": request,
			"echo":     fmt.Sprintf("您发送了: %v", request),
		},
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// 指标处理
func (app *App) metricsHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"uptime":      time.Since(startTime).String(),
		"environment": app.Config.Environment,
		"go_version":  "1.21",
		"memory":      "模拟内存使用情况",
		"cpu":         "模拟CPU使用情况",
		"requests":    "模拟请求统计",
	})
}

// 调试处理
func (app *App) debugHandler(c *gin.Context) {
	if app.Config.Environment == "production" {
		c.JSON(http.StatusForbidden, Response{
			Code:      403,
			Message:   "生产环境不允许访问调试信息",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"config":      app.Config,
		"environment": os.Environ(),
		"uptime":      time.Since(startTime).String(),
	})
}

// ============ 健康检查函数 ============

// 检查数据库
func (app *App) checkDatabase() string {
	// 模拟数据库连接检查
	// 在实际应用中，这里会尝试连接数据库
	if app.Config.DBHost == "" {
		return "not_configured"
	}
	return "ok"
}

// 检查Redis
func (app *App) checkRedis() string {
	// 模拟Redis连接检查
	// 在实际应用中，这里会尝试连接Redis
	if app.Config.RedisHost == "" {
		return "not_configured"
	}
	return "ok"
}

// 检查磁盘空间
func (app *App) checkDisk() string {
	// 模拟磁盘空间检查
	return "ok"
}

// 检查内存使用
func (app *App) checkMemory() string {
	// 模拟内存使用检查
	return "ok"
}

/*
Docker相关文件：

1. Dockerfile:
```dockerfile
# 多阶段构建
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# 运行阶段
FROM alpine:latest

# 安装ca证书
RUN apk --no-cache add ca-certificates

# 创建非root用户
RUN addgroup -g 1001 appgroup && adduser -u 1001 -G appgroup -s /bin/sh -D appuser

WORKDIR /root/

# 从构建阶段复制二进制文件
COPY --from=builder /app/main .

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# 运行应用
CMD ["./main"]
```

2. docker-compose.yml:
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - ENVIRONMENT=production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=myapp
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - LOG_LEVEL=info
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=myapp
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

3. .dockerignore:
```
.git
.gitignore
README.md
Dockerfile
.dockerignore
node_modules
npm-debug.log
coverage
.nyc_output
```

运行命令：
1. 构建镜像: docker build -t go-docker-app .
2. 运行容器: docker run -p 8080:8080 go-docker-app
3. 使用compose: docker-compose up -d

学习要点：
1. 多阶段构建减小镜像大小
2. 使用非root用户提高安全性
3. 健康检查监控容器状态
4. 环境变量配置应用
5. 容器编排管理多服务
6. 数据持久化使用卷
7. 网络配置和服务发现
8. 生产环境部署最佳实践
*/
