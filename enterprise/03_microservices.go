package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
)

/*
Go语言企业级开发 - 微服务架构基础

本文件涵盖：
1. 微服务架构概念
2. 服务注册与发现
3. 服务间通信
4. 负载均衡
5. 健康检查
6. 配置管理
7. 优雅关闭
*/

// ============ 服务注册中心 ============

// 服务实例
type ServiceInstance struct {
	ID       string    `json:"id"`
	Name     string    `json:"name"`
	Host     string    `json:"host"`
	Port     int       `json:"port"`
	Health   string    `json:"health"`
	Metadata map[string]string `json:"metadata"`
	LastSeen time.Time `json:"last_seen"`
}

// 服务注册中心
type ServiceRegistry struct {
	services map[string][]*ServiceInstance
	mutex    sync.RWMutex
}

func NewServiceRegistry() *ServiceRegistry {
	return &ServiceRegistry{
		services: make(map[string][]*ServiceInstance),
	}
}

// 注册服务
func (sr *ServiceRegistry) Register(instance *ServiceInstance) {
	sr.mutex.Lock()
	defer sr.mutex.Unlock()

	instance.LastSeen = time.Now()
	instance.Health = "healthy"

	if _, exists := sr.services[instance.Name]; !exists {
		sr.services[instance.Name] = make([]*ServiceInstance, 0)
	}

	// 检查是否已存在相同实例
	for i, existing := range sr.services[instance.Name] {
		if existing.ID == instance.ID {
			sr.services[instance.Name][i] = instance
			return
		}
	}

	// 添加新实例
	sr.services[instance.Name] = append(sr.services[instance.Name], instance)
	log.Printf("服务注册: %s (%s:%d)", instance.Name, instance.Host, instance.Port)
}

// 注销服务
func (sr *ServiceRegistry) Deregister(serviceName, instanceID string) {
	sr.mutex.Lock()
	defer sr.mutex.Unlock()

	instances := sr.services[serviceName]
	for i, instance := range instances {
		if instance.ID == instanceID {
			sr.services[serviceName] = append(instances[:i], instances[i+1:]...)
			log.Printf("服务注销: %s (%s)", serviceName, instanceID)
			break
		}
	}
}

// 发现服务
func (sr *ServiceRegistry) Discover(serviceName string) []*ServiceInstance {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	instances := sr.services[serviceName]
	var healthyInstances []*ServiceInstance

	for _, instance := range instances {
		if instance.Health == "healthy" {
			healthyInstances = append(healthyInstances, instance)
		}
	}

	return healthyInstances
}

// 健康检查
func (sr *ServiceRegistry) HealthCheck() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		sr.mutex.Lock()
		for serviceName, instances := range sr.services {
			for _, instance := range instances {
				// 检查服务是否超时
				if time.Since(instance.LastSeen) > 60*time.Second {
					instance.Health = "unhealthy"
					log.Printf("服务不健康: %s (%s)", serviceName, instance.ID)
				}
			}
		}
		sr.mutex.Unlock()
	}
}

// ============ 负载均衡器 ============

type LoadBalancer struct {
	strategy string
	counter  int
	mutex    sync.Mutex
}

func NewLoadBalancer(strategy string) *LoadBalancer {
	return &LoadBalancer{strategy: strategy}
}

// 选择服务实例
func (lb *LoadBalancer) Select(instances []*ServiceInstance) *ServiceInstance {
	if len(instances) == 0 {
		return nil
	}

	switch lb.strategy {
	case "round_robin":
		return lb.roundRobin(instances)
	case "random":
		return lb.random(instances)
	default:
		return instances[0]
	}
}

// 轮询策略
func (lb *LoadBalancer) roundRobin(instances []*ServiceInstance) *ServiceInstance {
	lb.mutex.Lock()
	defer lb.mutex.Unlock()

	instance := instances[lb.counter%len(instances)]
	lb.counter++
	return instance
}

// 随机策略
func (lb *LoadBalancer) random(instances []*ServiceInstance) *ServiceInstance {
	return instances[time.Now().UnixNano()%int64(len(instances))]
}

// ============ 服务客户端 ============

type ServiceClient struct {
	registry     *ServiceRegistry
	loadBalancer *LoadBalancer
	httpClient   *http.Client
}

func NewServiceClient(registry *ServiceRegistry, strategy string) *ServiceClient {
	return &ServiceClient{
		registry:     registry,
		loadBalancer: NewLoadBalancer(strategy),
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// 调用服务
func (sc *ServiceClient) Call(serviceName, path string, method string, body interface{}) (*http.Response, error) {
	// 发现服务实例
	instances := sc.registry.Discover(serviceName)
	if len(instances) == 0 {
		return nil, fmt.Errorf("没有可用的服务实例: %s", serviceName)
	}

	// 负载均衡选择实例
	instance := sc.loadBalancer.Select(instances)
	if instance == nil {
		return nil, fmt.Errorf("负载均衡器未选择到实例")
	}

	// 构建请求URL
	url := fmt.Sprintf("http://%s:%d%s", instance.Host, instance.Port, path)

	// 准备请求体
	var reqBody io.Reader
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("序列化请求体失败: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	// 创建请求
	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// 发送请求
	resp, err := sc.httpClient.Do(req)
	if err != nil {
		// 标记实例为不健康
		instance.Health = "unhealthy"
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	return resp, nil
}

// ============ 微服务基类 ============

type MicroService struct {
	Name     string
	Host     string
	Port     int
	ID       string
	Registry *ServiceRegistry
	Server   *http.Server
	Router   *gin.Engine
}

func NewMicroService(name, host string, port int, registry *ServiceRegistry) *MicroService {
	return &MicroService{
		Name:     name,
		Host:     host,
		Port:     port,
		ID:       fmt.Sprintf("%s-%d-%d", name, port, time.Now().Unix()),
		Registry: registry,
		Router:   gin.Default(),
	}
}

// 启动服务
func (ms *MicroService) Start() error {
	// 注册路由
	ms.setupRoutes()

	// 创建HTTP服务器
	ms.Server = &http.Server{
		Addr:    fmt.Sprintf("%s:%d", ms.Host, ms.Port),
		Handler: ms.Router,
	}

	// 注册到服务注册中心
	instance := &ServiceInstance{
		ID:   ms.ID,
		Name: ms.Name,
		Host: ms.Host,
		Port: ms.Port,
		Metadata: map[string]string{
			"version": "1.0.0",
		},
	}
	ms.Registry.Register(instance)

	// 启动心跳
	go ms.heartbeat()

	// 启动服务器
	log.Printf("微服务 %s 启动在 %s:%d", ms.Name, ms.Host, ms.Port)
	return ms.Server.ListenAndServe()
}

// 停止服务
func (ms *MicroService) Stop() error {
	// 从注册中心注销
	ms.Registry.Deregister(ms.Name, ms.ID)

	// 优雅关闭服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return ms.Server.Shutdown(ctx)
}

// 设置路由
func (ms *MicroService) setupRoutes() {
	// 健康检查
	ms.Router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   ms.Name,
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})

	// 服务信息
	ms.Router.GET("/info", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"id":   ms.ID,
			"name": ms.Name,
			"host": ms.Host,
			"port": ms.Port,
		})
	})
}

// 心跳
func (ms *MicroService) heartbeat() {
	ticker := time.NewTicker(15 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		instance := &ServiceInstance{
			ID:   ms.ID,
			Name: ms.Name,
			Host: ms.Host,
			Port: ms.Port,
			Metadata: map[string]string{
				"version": "1.0.0",
			},
		}
		ms.Registry.Register(instance)
	}
}

// ============ 用户服务 ============

type UserService struct {
	*MicroService
	users []User
}

type User struct {
	ID    int    `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

func NewUserService(host string, port int, registry *ServiceRegistry) *UserService {
	ms := NewMicroService("user-service", host, port, registry)
	us := &UserService{
		MicroService: ms,
		users: []User{
			{ID: 1, Name: "张三", Email: "<EMAIL>"},
			{ID: 2, Name: "李四", Email: "<EMAIL>"},
		},
	}

	// 添加用户服务特定路由
	us.Router.GET("/users", us.getUsers)
	us.Router.GET("/users/:id", us.getUser)
	us.Router.POST("/users", us.createUser)

	return us
}

func (us *UserService) getUsers(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"users": us.users,
		"total": len(us.users),
	})
}

func (us *UserService) getUser(c *gin.Context) {
	id := c.Param("id")
	for _, user := range us.users {
		if fmt.Sprintf("%d", user.ID) == id {
			c.JSON(http.StatusOK, user)
			return
		}
	}
	c.JSON(http.StatusNotFound, gin.H{"error": "用户不存在"})
}

func (us *UserService) createUser(c *gin.Context) {
	var user User
	if err := c.ShouldBindJSON(&user); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	user.ID = len(us.users) + 1
	us.users = append(us.users, user)
	c.JSON(http.StatusCreated, user)
}

// ============ 订单服务 ============

type OrderService struct {
	*MicroService
	orders []Order
	client *ServiceClient
}

type Order struct {
	ID     int `json:"id"`
	UserID int `json:"user_id"`
	Amount float64 `json:"amount"`
	Status string `json:"status"`
}

func NewOrderService(host string, port int, registry *ServiceRegistry) *OrderService {
	ms := NewMicroService("order-service", host, port, registry)
	os := &OrderService{
		MicroService: ms,
		orders:       []Order{},
		client:       NewServiceClient(registry, "round_robin"),
	}

	// 添加订单服务特定路由
	os.Router.GET("/orders", os.getOrders)
	os.Router.POST("/orders", os.createOrder)

	return os
}

func (os *OrderService) getOrders(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"orders": os.orders,
		"total":  len(os.orders),
	})
}

func (os *OrderService) createOrder(c *gin.Context) {
	var order Order
	if err := c.ShouldBindJSON(&order); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 调用用户服务验证用户
	resp, err := os.client.Call("user-service", fmt.Sprintf("/users/%d", order.UserID), "GET", nil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "调用用户服务失败: " + err.Error()})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户不存在"})
		return
	}

	// 创建订单
	order.ID = len(os.orders) + 1
	order.Status = "pending"
	os.orders = append(os.orders, order)

	c.JSON(http.StatusCreated, order)
}

// ============ 主函数 ============

func main() {
	fmt.Println("=== Go语言微服务架构学习 ===")

	// 创建服务注册中心
	registry := NewServiceRegistry()

	// 启动健康检查
	go registry.HealthCheck()

	// 创建服务实例
	userService := NewUserService("localhost", 8081, registry)
	orderService := NewOrderService("localhost", 8082, registry)

	// 启动服务
	go func() {
		if err := userService.Start(); err != nil && err != http.ErrServerClosed {
			log.Printf("用户服务启动失败: %v", err)
		}
	}()

	go func() {
		if err := orderService.Start(); err != nil && err != http.ErrServerClosed {
			log.Printf("订单服务启动失败: %v", err)
		}
	}()

	// 等待一段时间让服务启动
	time.Sleep(2 * time.Second)

	// 演示服务调用
	demonstrateServiceCalls(registry)

	// 优雅关闭
	gracefulShutdown(userService, orderService)
}

// 演示服务调用
func demonstrateServiceCalls(registry *ServiceRegistry) {
	fmt.Println("\n--- 服务调用演示 ---")

	client := NewServiceClient(registry, "round_robin")

	// 1. 调用用户服务
	fmt.Println("1. 调用用户服务:")
	resp, err := client.Call("user-service", "/users", "GET", nil)
	if err != nil {
		fmt.Printf("调用用户服务失败: %v\n", err)
	} else {
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		fmt.Printf("用户服务响应: %s\n", string(body))
	}

	// 2. 创建订单（会调用用户服务）
	fmt.Println("\n2. 创建订单:")
	order := map[string]interface{}{
		"user_id": 1,
		"amount":  99.99,
	}

	resp, err = client.Call("order-service", "/orders", "POST", order)
	if err != nil {
		fmt.Printf("创建订单失败: %v\n", err)
	} else {
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		fmt.Printf("订单服务响应: %s\n", string(body))
	}

	// 3. 查询订单
	fmt.Println("\n3. 查询订单:")
	resp, err = client.Call("order-service", "/orders", "GET", nil)
	if err != nil {
		fmt.Printf("查询订单失败: %v\n", err)
	} else {
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		fmt.Printf("订单列表: %s\n", string(body))
	}
}

// 优雅关闭
func gracefulShutdown(services ...*MicroService) {
	// 监听系统信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号
	<-sigChan
	fmt.Println("\n收到关闭信号，开始优雅关闭...")

	// 关闭所有服务
	var wg sync.WaitGroup
	for _, service := range services {
		wg.Add(1)
		go func(svc *MicroService) {
			defer wg.Done()
			if err := svc.Stop(); err != nil {
				log.Printf("关闭服务 %s 失败: %v", svc.Name, err)
			} else {
				log.Printf("服务 %s 已关闭", svc.Name)
			}
		}(service)
	}

	// 等待所有服务关闭
	wg.Wait()
	fmt.Println("所有服务已关闭")
}

/*
运行此程序的命令：
go run enterprise/03_microservices.go

测试API：
1. 用户服务: http://localhost:8081/users
2. 订单服务: http://localhost:8082/orders
3. 健康检查: http://localhost:8081/health

学习要点：
1. 微服务架构将单体应用拆分为独立的服务
2. 服务注册与发现实现服务间的动态通信
3. 负载均衡提高系统可用性和性能
4. 健康检查监控服务状态
5. 服务间通信通常使用HTTP/gRPC
6. 配置管理和服务治理是关键
7. 优雅关闭确保数据一致性
8. 监控和日志对微服务运维至关重要
*/
