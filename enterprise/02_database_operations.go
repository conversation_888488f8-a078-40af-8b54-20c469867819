package main

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	_ "github.com/mattn/go-sqlite3"
)

/*
Go语言企业级开发 - 数据库操作（GORM、原生SQL）

本文件涵盖：
1. GORM ORM框架使用
2. 原生SQL操作
3. 数据库连接管理
4. 模型定义和关联
5. 查询操作
6. 事务处理
7. 数据库迁移
*/

// ============ 模型定义 ============

// 用户模型
type User struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	Name      string    `gorm:"size:100;not null" json:"name"`
	Email     string    `gorm:"size:100;uniqueIndex;not null" json:"email"`
	Age       int       `gorm:"check:age >= 0" json:"age"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Posts     []Post    `gorm:"foreignKey:UserID" json:"posts,omitempty"`
	Profile   Profile   `gorm:"foreignKey:UserID" json:"profile,omitempty"`
}

// 文章模型
type Post struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	Title     string    `gorm:"size:200;not null" json:"title"`
	Content   string    `gorm:"type:text" json:"content"`
	UserID    uint      `gorm:"not null" json:"user_id"`
	User      User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Tags      []Tag     `gorm:"many2many:post_tags;" json:"tags,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// 用户资料模型
type Profile struct {
	ID       uint   `gorm:"primaryKey" json:"id"`
	UserID   uint   `gorm:"uniqueIndex;not null" json:"user_id"`
	Bio      string `gorm:"type:text" json:"bio"`
	Avatar   string `gorm:"size:255" json:"avatar"`
	Website  string `gorm:"size:255" json:"website"`
	Location string `gorm:"size:100" json:"location"`
}

// 标签模型
type Tag struct {
	ID    uint   `gorm:"primaryKey" json:"id"`
	Name  string `gorm:"size:50;uniqueIndex;not null" json:"name"`
	Posts []Post `gorm:"many2many:post_tags;" json:"posts,omitempty"`
}

// ============ 数据库管理器 ============

type DatabaseManager struct {
	gormDB *gorm.DB
	sqlDB  *sql.DB
}

func NewDatabaseManager() *DatabaseManager {
	return &DatabaseManager{}
}

// 初始化GORM连接
func (dm *DatabaseManager) InitGORM() error {
	// 配置日志
	newLogger := logger.New(
		log.New(log.Writer(), "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	// 连接数据库
	db, err := gorm.Open(sqlite.Open("test.db"), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	dm.gormDB = db
	return nil
}

// 初始化原生SQL连接
func (dm *DatabaseManager) InitSQL() error {
	db, err := sql.Open("sqlite3", "test_raw.db")
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}

	dm.sqlDB = db
	return nil
}

// 自动迁移
func (dm *DatabaseManager) AutoMigrate() error {
	return dm.gormDB.AutoMigrate(&User{}, &Post{}, &Profile{}, &Tag{})
}

// 关闭连接
func (dm *DatabaseManager) Close() error {
	if dm.sqlDB != nil {
		dm.sqlDB.Close()
	}
	if dm.gormDB != nil {
		sqlDB, err := dm.gormDB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

func main() {
	fmt.Println("=== Go语言数据库操作学习 ===")

	// 初始化数据库管理器
	dm := NewDatabaseManager()
	defer dm.Close()

	// 初始化GORM
	if err := dm.InitGORM(); err != nil {
		log.Fatal("GORM初始化失败:", err)
	}

	// 初始化原生SQL
	if err := dm.InitSQL(); err != nil {
		log.Fatal("SQL初始化失败:", err)
	}

	// 自动迁移
	if err := dm.AutoMigrate(); err != nil {
		log.Fatal("数据库迁移失败:", err)
	}

	// 演示GORM操作
	demonstrateGORM(dm)

	// 演示原生SQL操作
	demonstrateRawSQL(dm)

	// 演示事务处理
	demonstrateTransactions(dm)

	// 演示高级查询
	demonstrateAdvancedQueries(dm)
}

// ============ GORM操作演示 ============

func demonstrateGORM(dm *DatabaseManager) {
	fmt.Println("\n--- GORM操作演示 ---")

	db := dm.gormDB

	// 1. 创建用户
	fmt.Println("1. 创建用户:")
	user := User{
		Name:  "张三",
		Email: "<EMAIL>",
		Age:   25,
	}

	result := db.Create(&user)
	if result.Error != nil {
		fmt.Printf("创建用户失败: %v\n", result.Error)
	} else {
		fmt.Printf("创建用户成功: ID=%d\n", user.ID)
	}

	// 2. 批量创建
	fmt.Println("\n2. 批量创建用户:")
	users := []User{
		{Name: "李四", Email: "<EMAIL>", Age: 30},
		{Name: "王五", Email: "<EMAIL>", Age: 28},
	}

	result = db.Create(&users)
	fmt.Printf("批量创建 %d 个用户\n", result.RowsAffected)

	// 3. 查询操作
	fmt.Println("\n3. 查询操作:")

	// 根据ID查询
	var foundUser User
	db.First(&foundUser, user.ID)
	fmt.Printf("根据ID查询: %+v\n", foundUser)

	// 根据条件查询
	var userByEmail User
	db.Where("email = ?", "<EMAIL>").First(&userByEmail)
	fmt.Printf("根据邮箱查询: %+v\n", userByEmail)

	// 查询所有
	var allUsers []User
	db.Find(&allUsers)
	fmt.Printf("所有用户数量: %d\n", len(allUsers))

	// 4. 更新操作
	fmt.Println("\n4. 更新操作:")

	// 更新单个字段
	db.Model(&user).Update("age", 26)
	fmt.Printf("更新年龄后: %+v\n", user)

	// 更新多个字段
	db.Model(&user).Updates(User{Name: "张三三", Age: 27})
	fmt.Printf("更新多个字段后: %+v\n", user)

	// 5. 删除操作
	fmt.Println("\n5. 删除操作:")

	// 软删除（如果模型有DeletedAt字段）
	var userToDelete User
	db.Where("email = ?", "<EMAIL>").First(&userToDelete)
	db.Delete(&userToDelete)
	fmt.Printf("软删除用户: %d\n", userToDelete.ID)

	// 6. 关联操作
	fmt.Println("\n6. 关联操作:")

	// 创建用户资料
	profile := Profile{
		UserID:   user.ID,
		Bio:      "这是张三的个人简介",
		Location: "北京",
	}
	db.Create(&profile)

	// 创建文章
	post := Post{
		Title:   "我的第一篇文章",
		Content: "这是文章内容...",
		UserID:  user.ID,
	}
	db.Create(&post)

	// 预加载关联数据
	var userWithAssociations User
	db.Preload("Posts").Preload("Profile").First(&userWithAssociations, user.ID)
	fmt.Printf("用户及关联数据: %+v\n", userWithAssociations)
}

// ============ 原生SQL操作演示 ============

func demonstrateRawSQL(dm *DatabaseManager) {
	fmt.Println("\n--- 原生SQL操作演示 ---")

	db := dm.sqlDB

	// 1. 创建表
	fmt.Println("1. 创建表:")
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS customers (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		name TEXT NOT NULL,
		email TEXT UNIQUE NOT NULL,
		age INTEGER,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	)`

	_, err := db.Exec(createTableSQL)
	if err != nil {
		fmt.Printf("创建表失败: %v\n", err)
		return
	}
	fmt.Println("表创建成功")

	// 2. 插入数据
	fmt.Println("\n2. 插入数据:")
	insertSQL := `INSERT INTO customers (name, email, age) VALUES (?, ?, ?)`

	result, err := db.Exec(insertSQL, "赵六", "<EMAIL>", 32)
	if err != nil {
		fmt.Printf("插入数据失败: %v\n", err)
	} else {
		id, _ := result.LastInsertId()
		fmt.Printf("插入数据成功，ID: %d\n", id)
	}

	// 3. 查询数据
	fmt.Println("\n3. 查询数据:")
	querySQL := `SELECT id, name, email, age, created_at FROM customers WHERE age > ?`

	rows, err := db.Query(querySQL, 25)
	if err != nil {
		fmt.Printf("查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("查询结果:")
	for rows.Next() {
		var id int
		var name, email string
		var age int
		var createdAt string

		err := rows.Scan(&id, &name, &email, &age, &createdAt)
		if err != nil {
			fmt.Printf("扫描行失败: %v\n", err)
			continue
		}

		fmt.Printf("ID: %d, 姓名: %s, 邮箱: %s, 年龄: %d, 创建时间: %s\n",
			id, name, email, age, createdAt)
	}

	// 4. 更新数据
	fmt.Println("\n4. 更新数据:")
	updateSQL := `UPDATE customers SET age = ? WHERE email = ?`

	result, err = db.Exec(updateSQL, 33, "<EMAIL>")
	if err != nil {
		fmt.Printf("更新失败: %v\n", err)
	} else {
		rowsAffected, _ := result.RowsAffected()
		fmt.Printf("更新成功，影响行数: %d\n", rowsAffected)
	}

	// 5. 删除数据
	fmt.Println("\n5. 删除数据:")
	deleteSQL := `DELETE FROM customers WHERE age > ?`

	result, err = db.Exec(deleteSQL, 35)
	if err != nil {
		fmt.Printf("删除失败: %v\n", err)
	} else {
		rowsAffected, _ := result.RowsAffected()
		fmt.Printf("删除成功，影响行数: %d\n", rowsAffected)
	}
}

// ============ 事务处理演示 ============

func demonstrateTransactions(dm *DatabaseManager) {
	fmt.Println("\n--- 事务处理演示 ---")

	// GORM事务
	fmt.Println("1. GORM事务:")
	err := dm.gormDB.Transaction(func(tx *gorm.DB) error {
		// 创建用户
		user := User{
			Name:  "事务用户",
			Email: "<EMAIL>",
			Age:   25,
		}
		if err := tx.Create(&user).Error; err != nil {
			return err
		}

		// 创建文章
		post := Post{
			Title:   "事务文章",
			Content: "这是在事务中创建的文章",
			UserID:  user.ID,
		}
		if err := tx.Create(&post).Error; err != nil {
			return err
		}

		fmt.Printf("事务中创建用户ID: %d, 文章ID: %d\n", user.ID, post.ID)
		return nil
	})

	if err != nil {
		fmt.Printf("事务失败: %v\n", err)
	} else {
		fmt.Println("事务成功提交")
	}

	// 原生SQL事务
	fmt.Println("\n2. 原生SQL事务:")
	tx, err := dm.sqlDB.Begin()
	if err != nil {
		fmt.Printf("开始事务失败: %v\n", err)
		return
	}

	defer func() {
		if err != nil {
			tx.Rollback()
			fmt.Println("事务回滚")
		} else {
			tx.Commit()
			fmt.Println("事务提交")
		}
	}()

	// 在事务中执行操作
	_, err = tx.Exec("INSERT INTO customers (name, email, age) VALUES (?, ?, ?)",
		"事务客户", "<EMAIL>", 28)
	if err != nil {
		fmt.Printf("事务中插入失败: %v\n", err)
		return
	}

	fmt.Println("事务中操作成功")
}

// ============ 高级查询演示 ============

func demonstrateAdvancedQueries(dm *DatabaseManager) {
	fmt.Println("\n--- 高级查询演示 ---")

	db := dm.gormDB

	// 1. 条件查询
	fmt.Println("1. 条件查询:")
	var users []User
	db.Where("age > ? AND name LIKE ?", 20, "%三%").Find(&users)
	fmt.Printf("条件查询结果数量: %d\n", len(users))

	// 2. 排序和分页
	fmt.Println("\n2. 排序和分页:")
	var pagedUsers []User
	db.Order("age desc").Limit(2).Offset(0).Find(&pagedUsers)
	fmt.Printf("分页查询结果数量: %d\n", len(pagedUsers))

	// 3. 聚合查询
	fmt.Println("\n3. 聚合查询:")
	var count int64
	db.Model(&User{}).Count(&count)
	fmt.Printf("用户总数: %d\n", count)

	var avgAge float64
	db.Model(&User{}).Select("AVG(age)").Row().Scan(&avgAge)
	fmt.Printf("平均年龄: %.2f\n", avgAge)

	// 4. 分组查询
	fmt.Println("\n4. 分组查询:")
	type AgeGroup struct {
		Age   int
		Count int
	}
	var ageGroups []AgeGroup
	db.Model(&User{}).Select("age, count(*) as count").Group("age").Scan(&ageGroups)
	fmt.Printf("年龄分组: %+v\n", ageGroups)

	// 5. 子查询
	fmt.Println("\n5. 子查询:")
	var activeUsers []User
	subQuery := db.Model(&Post{}).Select("DISTINCT user_id")
	db.Where("id IN (?)", subQuery).Find(&activeUsers)
	fmt.Printf("有文章的用户数量: %d\n", len(activeUsers))

	// 6. 原生SQL查询
	fmt.Println("\n6. 原生SQL查询:")
	var results []map[string]interface{}
	db.Raw("SELECT name, age FROM users WHERE age > ?", 25).Scan(&results)
	fmt.Printf("原生SQL查询结果: %+v\n", results)
}

/*
运行此程序的命令：
go run enterprise/02_database_operations.go

学习要点：
1. GORM是功能强大的Go ORM库，支持多种数据库
2. 提供模型定义、自动迁移、关联关系等特性
3. 支持链式查询、预加载、事务等高级功能
4. 原生SQL适合复杂查询和性能优化场景
5. 事务确保数据一致性，支持回滚机制
6. 连接池管理提高数据库访问性能
7. 合理使用索引和查询优化提升性能
8. 注意SQL注入防护和数据验证
*/
