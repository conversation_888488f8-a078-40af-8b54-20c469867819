package main

import (
	"fmt"
	"io"
	"math"
	"sort"
	"strings"
)

/*
Go语言数据结构 - 接口

本文件涵盖：
1. 接口的定义和实现
2. 空接口和类型断言
3. 接口组合
4. 常用的标准库接口
5. 接口的最佳实践
6. 实用示例
*/

func main() {
	fmt.Println("=== Go语言接口学习 ===")
	
	// 1. 接口基础
	demonstrateInterfaceBasics()
	
	// 2. 空接口和类型断言
	demonstrateEmptyInterface()
	
	// 3. 接口组合
	demonstrateInterfaceComposition()
	
	// 4. 标准库接口
	demonstrateStandardInterfaces()
	
	// 5. 接口的多态性
	demonstratePolymorphism()
	
	// 6. 实用示例
	demonstratePracticalExamples()
}

// 定义基本接口
type Shape interface {
	Area() float64
	Perimeter() float64
}

// 定义扩展接口
type Drawable interface {
	Draw() string
}

// 定义组合接口
type DrawableShape interface {
	Shape    // 嵌入Shape接口
	Drawable // 嵌入Drawable接口
}

// 实现Shape接口的结构体
type Rectangle struct {
	Width, Height float64
}

func (r Rectangle) Area() float64 {
	return r.Width * r.Height
}

func (r Rectangle) Perimeter() float64 {
	return 2 * (r.Width + r.Height)
}

func (r Rectangle) Draw() string {
	return fmt.Sprintf("绘制矩形: %.1f x %.1f", r.Width, r.Height)
}

type Circle struct {
	Radius float64
}

func (c Circle) Area() float64 {
	return math.Pi * c.Radius * c.Radius
}

func (c Circle) Perimeter() float64 {
	return 2 * math.Pi * c.Radius
}

func (c Circle) Draw() string {
	return fmt.Sprintf("绘制圆形: 半径 %.1f", c.Radius)
}

// 只实现Shape接口的结构体
type Triangle struct {
	Base, Height float64
}

func (t Triangle) Area() float64 {
	return 0.5 * t.Base * t.Height
}

func (t Triangle) Perimeter() float64 {
	// 简化计算，假设是等腰三角形
	side := math.Sqrt(t.Base*t.Base/4 + t.Height*t.Height)
	return t.Base + 2*side
}

// 演示接口基础
func demonstrateInterfaceBasics() {
	fmt.Println("\n--- 接口基础 ---")
	
	// 创建实现了Shape接口的对象
	var shapes []Shape
	shapes = append(shapes, Rectangle{Width: 5, Height: 3})
	shapes = append(shapes, Circle{Radius: 2})
	shapes = append(shapes, Triangle{Base: 4, Height: 3})
	
	// 通过接口调用方法
	fmt.Println("图形信息:")
	for i, shape := range shapes {
		fmt.Printf("  图形%d: 面积=%.2f, 周长=%.2f\n", 
			i+1, shape.Area(), shape.Perimeter())
	}
	
	// 接口的零值是nil
	var shape Shape
	fmt.Printf("接口零值: %v (是否为nil: %t)\n", shape, shape == nil)
	
	// 接口赋值
	shape = Rectangle{Width: 10, Height: 5}
	fmt.Printf("赋值后: 面积=%.2f\n", shape.Area())
	
	// 接口类型检查
	if rect, ok := shape.(Rectangle); ok {
		fmt.Printf("这是一个矩形: %.1f x %.1f\n", rect.Width, rect.Height)
	}
}

// 演示空接口和类型断言
func demonstrateEmptyInterface() {
	fmt.Println("\n--- 空接口和类型断言 ---")
	
	// 空接口可以存储任何类型的值
	var anything interface{}
	
	anything = 42
	fmt.Printf("存储整数: %v (类型: %T)\n", anything, anything)
	
	anything = "Hello, World!"
	fmt.Printf("存储字符串: %v (类型: %T)\n", anything, anything)
	
	anything = []int{1, 2, 3}
	fmt.Printf("存储切片: %v (类型: %T)\n", anything, anything)
	
	anything = Rectangle{Width: 5, Height: 3}
	fmt.Printf("存储结构体: %v (类型: %T)\n", anything, anything)
	
	// 类型断言
	fmt.Println("\n类型断言:")
	
	// 安全的类型断言
	if value, ok := anything.(Rectangle); ok {
		fmt.Printf("成功断言为Rectangle: %+v\n", value)
	} else {
		fmt.Println("断言失败")
	}
	
	// 不安全的类型断言（如果类型不匹配会panic）
	// value := anything.(string) // 这会panic，因为anything现在是Rectangle
	
	// 类型switch
	fmt.Println("\n类型switch:")
	values := []interface{}{
		42,
		"hello",
		3.14,
		true,
		[]int{1, 2, 3},
		Rectangle{Width: 2, Height: 3},
	}
	
	for i, v := range values {
		switch val := v.(type) {
		case int:
			fmt.Printf("  值%d: 整数 %d\n", i, val)
		case string:
			fmt.Printf("  值%d: 字符串 %s\n", i, val)
		case float64:
			fmt.Printf("  值%d: 浮点数 %.2f\n", i, val)
		case bool:
			fmt.Printf("  值%d: 布尔值 %t\n", i, val)
		case []int:
			fmt.Printf("  值%d: 整数切片 %v\n", i, val)
		case Rectangle:
			fmt.Printf("  值%d: 矩形 %.1fx%.1f\n", i, val.Width, val.Height)
		default:
			fmt.Printf("  值%d: 未知类型 %T\n", i, val)
		}
	}
}

// 演示接口组合
func demonstrateInterfaceComposition() {
	fmt.Println("\n--- 接口组合 ---")
	
	// Rectangle和Circle都实现了DrawableShape接口
	var drawableShapes []DrawableShape
	drawableShapes = append(drawableShapes, Rectangle{Width: 4, Height: 2})
	drawableShapes = append(drawableShapes, Circle{Radius: 3})
	
	fmt.Println("可绘制图形:")
	for i, shape := range drawableShapes {
		fmt.Printf("  图形%d: %s\n", i+1, shape.Draw())
		fmt.Printf("    面积: %.2f, 周长: %.2f\n", shape.Area(), shape.Perimeter())
	}
	
	// Triangle只实现了Shape接口，不能赋值给DrawableShape
	triangle := Triangle{Base: 3, Height: 4}
	var shape Shape = triangle // 可以赋值给Shape
	fmt.Printf("三角形面积: %.2f\n", shape.Area())
	
	// var drawableShape DrawableShape = triangle // 编译错误：Triangle没有实现Draw方法
}

// 演示标准库接口
func demonstrateStandardInterfaces() {
	fmt.Println("\n--- 标准库接口 ---")
	
	// 1. fmt.Stringer接口
	fmt.Println("1. fmt.Stringer接口:")
	person := Person{Name: "张三", Age: 25}
	fmt.Printf("  %s\n", person) // 自动调用String()方法
	
	// 2. sort.Interface接口
	fmt.Println("2. sort.Interface接口:")
	people := People{
		{Name: "张三", Age: 25},
		{Name: "李四", Age: 30},
		{Name: "王五", Age: 20},
	}
	fmt.Printf("  排序前: %v\n", people)
	sort.Sort(people)
	fmt.Printf("  排序后: %v\n", people)
	
	// 3. io.Writer接口
	fmt.Println("3. io.Writer接口:")
	var buf strings.Builder
	writeToWriter(&buf, "Hello, ")
	writeToWriter(&buf, "World!")
	fmt.Printf("  写入结果: %s\n", buf.String())
	
	// 4. error接口
	fmt.Println("4. error接口:")
	err := divide(10, 0)
	if err != nil {
		fmt.Printf("  错误: %v\n", err)
	}
	
	result, err := divideWithResult(10, 2)
	if err != nil {
		fmt.Printf("  错误: %v\n", err)
	} else {
		fmt.Printf("  结果: %.2f\n", result)
	}
}

// 实现fmt.Stringer接口
type Person struct {
	Name string
	Age  int
}

func (p Person) String() string {
	return fmt.Sprintf("Person{Name: %s, Age: %d}", p.Name, p.Age)
}

// 实现sort.Interface接口
type People []Person

func (p People) Len() int           { return len(p) }
func (p People) Less(i, j int) bool { return p[i].Age < p[j].Age }
func (p People) Swap(i, j int)      { p[i], p[j] = p[j], p[i] }

// 使用io.Writer接口
func writeToWriter(w io.Writer, text string) {
	w.Write([]byte(text))
}

// 实现error接口
type DivisionError struct {
	Dividend float64
	Divisor  float64
}

func (e DivisionError) Error() string {
	return fmt.Sprintf("除法错误: %.2f / %.2f - 除数不能为零", e.Dividend, e.Divisor)
}

func divide(a, b float64) error {
	if b == 0 {
		return DivisionError{Dividend: a, Divisor: b}
	}
	return nil
}

func divideWithResult(a, b float64) (float64, error) {
	if b == 0 {
		return 0, DivisionError{Dividend: a, Divisor: b}
	}
	return a / b, nil
}

// 演示多态性
func demonstratePolymorphism() {
	fmt.Println("\n--- 接口的多态性 ---")
	
	// 定义一个处理Shape的函数
	processShape := func(s Shape) {
		fmt.Printf("  处理图形: 面积=%.2f, 周长=%.2f\n", s.Area(), s.Perimeter())
		
		// 根据具体类型执行不同操作
		switch shape := s.(type) {
		case Rectangle:
			fmt.Printf("    这是矩形: %.1f x %.1f\n", shape.Width, shape.Height)
		case Circle:
			fmt.Printf("    这是圆形: 半径 %.1f\n", shape.Radius)
		case Triangle:
			fmt.Printf("    这是三角形: 底边 %.1f, 高 %.1f\n", shape.Base, shape.Height)
		}
	}
	
	shapes := []Shape{
		Rectangle{Width: 5, Height: 3},
		Circle{Radius: 2},
		Triangle{Base: 4, Height: 3},
	}
	
	fmt.Println("多态处理不同图形:")
	for i, shape := range shapes {
		fmt.Printf("图形%d:\n", i+1)
		processShape(shape)
	}
}

// 演示实用示例
func demonstratePracticalExamples() {
	fmt.Println("\n--- 实用示例 ---")
	
	// 1. 数据库接口示例
	demonstrateDatabase()
	
	// 2. 支付接口示例
	demonstratePayment()
	
	// 3. 日志接口示例
	demonstrateLogger()
}

// 数据库接口示例
type Database interface {
	Connect() error
	Query(sql string) ([]map[string]interface{}, error)
	Close() error
}

type MySQL struct {
	Host     string
	Port     int
	Username string
	Database string
}

func (m *MySQL) Connect() error {
	fmt.Printf("连接到MySQL: %s:%d/%s\n", m.Host, m.Port, m.Database)
	return nil
}

func (m *MySQL) Query(sql string) ([]map[string]interface{}, error) {
	fmt.Printf("执行MySQL查询: %s\n", sql)
	return []map[string]interface{}{
		{"id": 1, "name": "张三"},
		{"id": 2, "name": "李四"},
	}, nil
}

func (m *MySQL) Close() error {
	fmt.Println("关闭MySQL连接")
	return nil
}

type PostgreSQL struct {
	ConnectionString string
}

func (p *PostgreSQL) Connect() error {
	fmt.Printf("连接到PostgreSQL: %s\n", p.ConnectionString)
	return nil
}

func (p *PostgreSQL) Query(sql string) ([]map[string]interface{}, error) {
	fmt.Printf("执行PostgreSQL查询: %s\n", sql)
	return []map[string]interface{}{
		{"id": 1, "name": "王五"},
		{"id": 2, "name": "赵六"},
	}, nil
}

func (p *PostgreSQL) Close() error {
	fmt.Println("关闭PostgreSQL连接")
	return nil
}

func demonstrateDatabase() {
	fmt.Println("数据库接口示例:")
	
	databases := []Database{
		&MySQL{Host: "localhost", Port: 3306, Username: "root", Database: "test"},
		&PostgreSQL{ConnectionString: "postgres://user:pass@localhost/test"},
	}
	
	for i, db := range databases {
		fmt.Printf("  数据库%d:\n", i+1)
		db.Connect()
		results, _ := db.Query("SELECT * FROM users")
		fmt.Printf("    查询结果: %v\n", results)
		db.Close()
	}
}

// 支付接口示例
type PaymentProcessor interface {
	ProcessPayment(amount float64) error
	GetTransactionFee(amount float64) float64
}

type CreditCard struct {
	CardNumber string
	CVV        string
}

func (cc *CreditCard) ProcessPayment(amount float64) error {
	fmt.Printf("信用卡支付: ¥%.2f (卡号: %s)\n", amount, cc.CardNumber)
	return nil
}

func (cc *CreditCard) GetTransactionFee(amount float64) float64 {
	return amount * 0.03 // 3%手续费
}

type PayPal struct {
	Email string
}

func (pp *PayPal) ProcessPayment(amount float64) error {
	fmt.Printf("PayPal支付: ¥%.2f (邮箱: %s)\n", amount, pp.Email)
	return nil
}

func (pp *PayPal) GetTransactionFee(amount float64) float64 {
	return amount * 0.025 // 2.5%手续费
}

type Alipay struct {
	Account string
}

func (ap *Alipay) ProcessPayment(amount float64) error {
	fmt.Printf("支付宝支付: ¥%.2f (账号: %s)\n", amount, ap.Account)
	return nil
}

func (ap *Alipay) GetTransactionFee(amount float64) float64 {
	return 0 // 免手续费
}

func demonstratePayment() {
	fmt.Println("\n支付接口示例:")
	
	processors := []PaymentProcessor{
		&CreditCard{CardNumber: "****-****-****-1234", CVV: "123"},
		&PayPal{Email: "<EMAIL>"},
		&Alipay{Account: "***********"},
	}
	
	amount := 100.0
	
	for i, processor := range processors {
		fmt.Printf("  支付方式%d:\n", i+1)
		fee := processor.GetTransactionFee(amount)
		total := amount + fee
		fmt.Printf("    手续费: ¥%.2f, 总计: ¥%.2f\n", fee, total)
		processor.ProcessPayment(total)
	}
}

// 日志接口示例
type Logger interface {
	Info(message string)
	Warning(message string)
	Error(message string)
}

type ConsoleLogger struct{}

func (cl *ConsoleLogger) Info(message string) {
	fmt.Printf("[INFO] %s\n", message)
}

func (cl *ConsoleLogger) Warning(message string) {
	fmt.Printf("[WARNING] %s\n", message)
}

func (cl *ConsoleLogger) Error(message string) {
	fmt.Printf("[ERROR] %s\n", message)
}

type FileLogger struct {
	Filename string
}

func (fl *FileLogger) Info(message string) {
	fmt.Printf("[INFO -> %s] %s\n", fl.Filename, message)
}

func (fl *FileLogger) Warning(message string) {
	fmt.Printf("[WARNING -> %s] %s\n", fl.Filename, message)
}

func (fl *FileLogger) Error(message string) {
	fmt.Printf("[ERROR -> %s] %s\n", fl.Filename, message)
}

func demonstrateLogger() {
	fmt.Println("\n日志接口示例:")
	
	loggers := []Logger{
		&ConsoleLogger{},
		&FileLogger{Filename: "app.log"},
	}
	
	for i, logger := range loggers {
		fmt.Printf("  日志器%d:\n", i+1)
		logger.Info("应用程序启动")
		logger.Warning("配置文件未找到，使用默认配置")
		logger.Error("数据库连接失败")
	}
}

/*
运行此程序的命令：
go run data-structures/04_interfaces.go

学习要点：
1. 接口定义了方法签名，任何实现了这些方法的类型都实现了该接口
2. Go使用隐式接口实现，不需要显式声明实现关系
3. 空接口interface{}可以存储任何类型的值
4. 类型断言用于从接口中提取具体类型的值
5. 接口组合通过嵌入其他接口来扩展功能
6. 标准库定义了许多有用的接口，如Stringer、Writer、error等
7. 接口实现了多态性，同一接口可以有不同的实现
8. 接口是Go语言实现解耦和可测试代码的重要工具
*/
