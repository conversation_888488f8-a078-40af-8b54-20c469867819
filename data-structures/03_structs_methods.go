package main

import (
	"fmt"
	"math"
	"time"
)

/*
Go语言数据结构 - 结构体和方法

本文件涵盖：
1. 结构体的定义和初始化
2. 结构体字段访问和修改
3. 结构体方法的定义
4. 值接收者vs指针接收者
5. 结构体嵌入（组合）
6. 结构体标签
7. 实用示例
*/

func main() {
	fmt.Println("=== Go语言结构体和方法学习 ===")
	
	// 1. 结构体基础
	demonstrateStructBasics()
	
	// 2. 结构体方法
	demonstrateStructMethods()
	
	// 3. 指针接收者vs值接收者
	demonstrateReceivers()
	
	// 4. 结构体嵌入
	demonstrateStructEmbedding()
	
	// 5. 结构体标签
	demonstrateStructTags()
	
	// 6. 实用示例
	demonstratePracticalExamples()
}

// 定义基本结构体
type Person struct {
	Name string
	Age  int
	City string
}

// 定义带有不同类型字段的结构体
type Student struct {
	ID       int
	Name     string
	Age      int
	Scores   []float64
	IsActive bool
	Metadata map[string]string
}

// 演示结构体基础
func demonstrateStructBasics() {
	fmt.Println("\n--- 结构体基础 ---")
	
	// 1. 结构体零值
	var p1 Person
	fmt.Printf("零值结构体: %+v\n", p1)
	
	// 2. 结构体字面量初始化
	p2 := Person{
		Name: "张三",
		Age:  25,
		City: "北京",
	}
	fmt.Printf("字面量初始化: %+v\n", p2)
	
	// 3. 按字段顺序初始化（不推荐）
	p3 := Person{"李四", 30, "上海"}
	fmt.Printf("按顺序初始化: %+v\n", p3)
	
	// 4. 部分初始化
	p4 := Person{
		Name: "王五",
		Age:  28,
		// City 使用零值
	}
	fmt.Printf("部分初始化: %+v\n", p4)
	
	// 5. 使用new创建结构体指针
	p5 := new(Person)
	p5.Name = "赵六"
	p5.Age = 35
	p5.City = "深圳"
	fmt.Printf("new创建的结构体: %+v\n", *p5)
	
	// 6. 结构体指针字面量
	p6 := &Person{
		Name: "钱七",
		Age:  40,
		City: "广州",
	}
	fmt.Printf("结构体指针: %+v\n", *p6)
	
	// 7. 字段访问和修改
	fmt.Printf("修改前: %s, %d岁\n", p2.Name, p2.Age)
	p2.Age = 26
	fmt.Printf("修改后: %s, %d岁\n", p2.Name, p2.Age)
	
	// 8. 通过指针访问字段
	fmt.Printf("通过指针访问: %s\n", p6.Name) // Go自动解引用
	(*p6).Age = 41 // 显式解引用
	fmt.Printf("修改后的年龄: %d\n", p6.Age)
	
	// 9. 结构体比较
	p7 := Person{"张三", 25, "北京"}
	p8 := Person{"张三", 25, "北京"}
	fmt.Printf("结构体相等: %t\n", p7 == p8)
	
	// 10. 复杂结构体
	s1 := Student{
		ID:     1001,
		Name:   "学生A",
		Age:    20,
		Scores: []float64{85.5, 92.0, 78.5},
		IsActive: true,
		Metadata: map[string]string{
			"class": "计算机科学",
			"year":  "2023",
		},
	}
	fmt.Printf("复杂结构体: %+v\n", s1)
}

// 为Person定义方法
func (p Person) String() string {
	return fmt.Sprintf("Person{Name: %s, Age: %d, City: %s}", p.Name, p.Age, p.City)
}

func (p Person) Greet() string {
	return fmt.Sprintf("你好，我是%s，来自%s", p.Name, p.City)
}

func (p Person) IsAdult() bool {
	return p.Age >= 18
}

// 值接收者方法：不会修改原结构体
func (p Person) HaveBirthday() {
	p.Age++
	fmt.Printf("生日快乐！现在%d岁了\n", p.Age)
}

// 指针接收者方法：会修改原结构体
func (p *Person) HaveBirthdayPointer() {
	p.Age++
	fmt.Printf("生日快乐！现在%d岁了\n", p.Age)
}

func (p *Person) MoveTo(newCity string) {
	oldCity := p.City
	p.City = newCity
	fmt.Printf("%s从%s搬到了%s\n", p.Name, oldCity, newCity)
}

// 演示结构体方法
func demonstrateStructMethods() {
	fmt.Println("\n--- 结构体方法 ---")
	
	person := Person{
		Name: "张三",
		Age:  25,
		City: "北京",
	}
	
	// 调用方法
	fmt.Println(person.String())
	fmt.Println(person.Greet())
	fmt.Printf("是否成年: %t\n", person.IsAdult())
	
	// 方法可以链式调用（如果返回相同类型）
	fmt.Printf("成年状态: %t\n", person.IsAdult())
}

// 演示接收者类型的区别
func demonstrateReceivers() {
	fmt.Println("\n--- 接收者类型 ---")
	
	person := Person{
		Name: "李四",
		Age:  30,
		City: "上海",
	}
	
	fmt.Printf("原始年龄: %d\n", person.Age)
	
	// 值接收者：不会修改原结构体
	person.HaveBirthday()
	fmt.Printf("值接收者调用后: %d\n", person.Age) // 年龄没有改变
	
	// 指针接收者：会修改原结构体
	person.HaveBirthdayPointer()
	fmt.Printf("指针接收者调用后: %d\n", person.Age) // 年龄改变了
	
	// 移动城市
	person.MoveTo("深圳")
	fmt.Printf("移动后: %+v\n", person)
	
	// Go可以自动转换值和指针
	personPtr := &person
	fmt.Println("通过指针调用值接收者方法:", personPtr.Greet())
	
	// 值也可以调用指针接收者方法（Go自动取地址）
	person.MoveTo("广州")
}

// 定义嵌入结构体
type Address struct {
	Street   string
	City     string
	Province string
	ZipCode  string
}

func (a Address) String() string {
	return fmt.Sprintf("%s, %s, %s %s", a.Street, a.City, a.Province, a.ZipCode)
}

func (a Address) FullAddress() string {
	return fmt.Sprintf("地址: %s", a.String())
}

// 嵌入Address的Employee结构体
type Employee struct {
	ID       int
	Name     string
	Position string
	Salary   float64
	Address  // 嵌入结构体
}

// Employee的方法
func (e Employee) String() string {
	return fmt.Sprintf("Employee{ID: %d, Name: %s, Position: %s, Salary: %.2f, Address: %s}",
		e.ID, e.Name, e.Position, e.Salary, e.Address.String())
}

func (e Employee) GetInfo() string {
	return fmt.Sprintf("%s (ID: %d) - %s, 薪资: ¥%.2f", e.Name, e.ID, e.Position, e.Salary)
}

// 匿名嵌入的结构体
type Manager struct {
	Employee    // 匿名嵌入
	TeamSize int
	Budget   float64
}

func (m Manager) GetTeamInfo() string {
	return fmt.Sprintf("管理团队规模: %d人, 预算: ¥%.2f", m.TeamSize, m.Budget)
}

// 演示结构体嵌入
func demonstrateStructEmbedding() {
	fmt.Println("\n--- 结构体嵌入 ---")
	
	// 1. 命名嵌入
	emp := Employee{
		ID:       1001,
		Name:     "王经理",
		Position: "软件工程师",
		Salary:   15000,
		Address: Address{
			Street:   "中关村大街1号",
			City:     "北京",
			Province: "北京市",
			ZipCode:  "100080",
		},
	}
	
	fmt.Println("员工信息:", emp.String())
	fmt.Println("员工详情:", emp.GetInfo())
	fmt.Println("员工地址:", emp.Address.FullAddress())
	
	// 2. 匿名嵌入
	manager := Manager{
		Employee: Employee{
			ID:       2001,
			Name:     "张总监",
			Position: "技术总监",
			Salary:   25000,
			Address: Address{
				Street:   "金融街15号",
				City:     "北京",
				Province: "北京市",
				ZipCode:  "100033",
			},
		},
		TeamSize: 10,
		Budget:   500000,
	}
	
	fmt.Println("管理者信息:", manager.String()) // 继承了Employee的方法
	fmt.Println("管理者详情:", manager.GetInfo()) // 继承了Employee的方法
	fmt.Println("团队信息:", manager.GetTeamInfo())
	
	// 可以直接访问嵌入结构体的字段
	fmt.Printf("管理者姓名: %s\n", manager.Name) // 直接访问Employee.Name
	fmt.Printf("管理者城市: %s\n", manager.City) // 直接访问Employee.Address.City
	
	// 3. 方法提升
	fmt.Println("地址信息:", manager.FullAddress()) // Address的方法被提升到Manager
}

// 带标签的结构体
type User struct {
	ID       int    `json:"id" db:"user_id" validate:"required"`
	Username string `json:"username" db:"username" validate:"required,min=3,max=20"`
	Email    string `json:"email" db:"email" validate:"required,email"`
	Age      int    `json:"age" db:"age" validate:"min=0,max=120"`
	IsActive bool   `json:"is_active" db:"is_active"`
	Created  time.Time `json:"created_at" db:"created_at"`
}

// 演示结构体标签
func demonstrateStructTags() {
	fmt.Println("\n--- 结构体标签 ---")
	
	user := User{
		ID:       1,
		Username: "zhangsan",
		Email:    "<EMAIL>",
		Age:      25,
		IsActive: true,
		Created:  time.Now(),
	}
	
	fmt.Printf("用户结构体: %+v\n", user)
	
	// 使用反射读取标签（在实际应用中，通常由JSON、数据库等库使用）
	fmt.Println("结构体标签信息:")
	printStructTags(user)
}

// 使用反射打印结构体标签
func printStructTags(v interface{}) {
	// 这里只是演示概念，实际的反射代码会在反射章节详细介绍
	fmt.Println("  标签用于JSON序列化、数据库映射、验证等")
	fmt.Println("  json标签: 控制JSON序列化的字段名")
	fmt.Println("  db标签: 控制数据库字段映射")
	fmt.Println("  validate标签: 控制字段验证规则")
}

// 演示实用示例
func demonstratePracticalExamples() {
	fmt.Println("\n--- 实用示例 ---")
	
	// 1. 实现一个简单的几何图形系统
	demonstrateGeometry()
	
	// 2. 实现一个简单的银行账户系统
	demonstrateBankAccount()
	
	// 3. 实现一个简单的任务管理系统
	demonstrateTaskManager()
}

// 几何图形示例
type Point struct {
	X, Y float64
}

func (p Point) String() string {
	return fmt.Sprintf("(%.2f, %.2f)", p.X, p.Y)
}

func (p Point) DistanceTo(other Point) float64 {
	dx := p.X - other.X
	dy := p.Y - other.Y
	return math.Sqrt(dx*dx + dy*dy)
}

type Circle struct {
	Center Point
	Radius float64
}

func (c Circle) Area() float64 {
	return math.Pi * c.Radius * c.Radius
}

func (c Circle) Circumference() float64 {
	return 2 * math.Pi * c.Radius
}

func (c Circle) Contains(p Point) bool {
	return c.Center.DistanceTo(p) <= c.Radius
}

type Rectangle struct {
	TopLeft     Point
	BottomRight Point
}

func (r Rectangle) Area() float64 {
	width := r.BottomRight.X - r.TopLeft.X
	height := r.TopLeft.Y - r.BottomRight.Y
	return width * height
}

func (r Rectangle) Perimeter() float64 {
	width := r.BottomRight.X - r.TopLeft.X
	height := r.TopLeft.Y - r.BottomRight.Y
	return 2 * (width + height)
}

func demonstrateGeometry() {
	fmt.Println("几何图形示例:")
	
	// 点
	p1 := Point{0, 0}
	p2 := Point{3, 4}
	fmt.Printf("点1: %s, 点2: %s\n", p1, p2)
	fmt.Printf("两点距离: %.2f\n", p1.DistanceTo(p2))
	
	// 圆
	circle := Circle{
		Center: Point{0, 0},
		Radius: 5,
	}
	fmt.Printf("圆心: %s, 半径: %.2f\n", circle.Center, circle.Radius)
	fmt.Printf("圆面积: %.2f\n", circle.Area())
	fmt.Printf("圆周长: %.2f\n", circle.Circumference())
	fmt.Printf("点(3,4)是否在圆内: %t\n", circle.Contains(p2))
	
	// 矩形
	rect := Rectangle{
		TopLeft:     Point{0, 5},
		BottomRight: Point{4, 0},
	}
	fmt.Printf("矩形面积: %.2f\n", rect.Area())
	fmt.Printf("矩形周长: %.2f\n", rect.Perimeter())
}

// 银行账户示例
type BankAccount struct {
	AccountNumber string
	HolderName    string
	Balance       float64
	IsActive      bool
}

func NewBankAccount(accountNumber, holderName string) *BankAccount {
	return &BankAccount{
		AccountNumber: accountNumber,
		HolderName:    holderName,
		Balance:       0,
		IsActive:      true,
	}
}

func (ba *BankAccount) Deposit(amount float64) error {
	if !ba.IsActive {
		return fmt.Errorf("账户已冻结")
	}
	if amount <= 0 {
		return fmt.Errorf("存款金额必须大于0")
	}
	ba.Balance += amount
	return nil
}

func (ba *BankAccount) Withdraw(amount float64) error {
	if !ba.IsActive {
		return fmt.Errorf("账户已冻结")
	}
	if amount <= 0 {
		return fmt.Errorf("取款金额必须大于0")
	}
	if amount > ba.Balance {
		return fmt.Errorf("余额不足")
	}
	ba.Balance -= amount
	return nil
}

func (ba *BankAccount) GetBalance() float64 {
	return ba.Balance
}

func (ba *BankAccount) Freeze() {
	ba.IsActive = false
}

func (ba *BankAccount) Unfreeze() {
	ba.IsActive = true
}

func demonstrateBankAccount() {
	fmt.Println("\n银行账户示例:")
	
	account := NewBankAccount("*********", "张三")
	fmt.Printf("创建账户: %s (%s)\n", account.HolderName, account.AccountNumber)
	
	// 存款
	if err := account.Deposit(1000); err != nil {
		fmt.Printf("存款失败: %v\n", err)
	} else {
		fmt.Printf("存款成功，余额: ¥%.2f\n", account.GetBalance())
	}
	
	// 取款
	if err := account.Withdraw(300); err != nil {
		fmt.Printf("取款失败: %v\n", err)
	} else {
		fmt.Printf("取款成功，余额: ¥%.2f\n", account.GetBalance())
	}
	
	// 尝试取款超过余额
	if err := account.Withdraw(1000); err != nil {
		fmt.Printf("取款失败: %v\n", err)
	}
	
	// 冻结账户
	account.Freeze()
	if err := account.Deposit(100); err != nil {
		fmt.Printf("存款失败: %v\n", err)
	}
}

// 任务管理示例
type Task struct {
	ID          int
	Title       string
	Description string
	Status      string
	Priority    int
	Created     time.Time
	Updated     time.Time
}

func NewTask(id int, title, description string, priority int) *Task {
	now := time.Now()
	return &Task{
		ID:          id,
		Title:       title,
		Description: description,
		Status:      "待办",
		Priority:    priority,
		Created:     now,
		Updated:     now,
	}
}

func (t *Task) Start() {
	t.Status = "进行中"
	t.Updated = time.Now()
}

func (t *Task) Complete() {
	t.Status = "已完成"
	t.Updated = time.Now()
}

func (t *Task) UpdateDescription(description string) {
	t.Description = description
	t.Updated = time.Now()
}

func (t Task) String() string {
	return fmt.Sprintf("任务[%d]: %s (%s) - 优先级: %d", 
		t.ID, t.Title, t.Status, t.Priority)
}

type TaskManager struct {
	tasks  map[int]*Task
	nextID int
}

func NewTaskManager() *TaskManager {
	return &TaskManager{
		tasks:  make(map[int]*Task),
		nextID: 1,
	}
}

func (tm *TaskManager) AddTask(title, description string, priority int) *Task {
	task := NewTask(tm.nextID, title, description, priority)
	tm.tasks[tm.nextID] = task
	tm.nextID++
	return task
}

func (tm *TaskManager) GetTask(id int) (*Task, bool) {
	task, exists := tm.tasks[id]
	return task, exists
}

func (tm *TaskManager) ListTasks() []*Task {
	tasks := make([]*Task, 0, len(tm.tasks))
	for _, task := range tm.tasks {
		tasks = append(tasks, task)
	}
	return tasks
}

func demonstrateTaskManager() {
	fmt.Println("\n任务管理示例:")
	
	tm := NewTaskManager()
	
	// 添加任务
	task1 := tm.AddTask("学习Go语言", "完成Go语言基础教程", 1)
	task2 := tm.AddTask("写项目文档", "编写项目的README文档", 2)
	task3 := tm.AddTask("代码审查", "审查团队成员的代码", 1)
	
	fmt.Println("添加的任务:")
	for _, task := range tm.ListTasks() {
		fmt.Printf("  %s\n", task)
	}
	
	// 开始任务
	task1.Start()
	fmt.Printf("开始任务: %s\n", task1)
	
	// 完成任务
	task1.Complete()
	fmt.Printf("完成任务: %s\n", task1)
	
	// 更新任务描述
	task2.UpdateDescription("编写详细的项目README文档，包括安装和使用说明")
	fmt.Printf("更新任务: %s\n", task2)
}

/*
运行此程序的命令：
go run data-structures/03_structs_methods.go

学习要点：
1. 结构体是值类型，可以通过字面量、new或&操作符创建
2. 方法通过接收者与结构体关联，接收者可以是值或指针
3. 指针接收者可以修改结构体，值接收者不能
4. 结构体嵌入实现了类似继承的功能，支持方法提升
5. 结构体标签用于元数据，常用于JSON、数据库映射等
6. Go的组合优于继承的设计哲学通过结构体嵌入体现
7. 结构体方法可以实现复杂的业务逻辑和数据封装
*/
