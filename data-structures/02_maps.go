package main

import (
	"fmt"
	"sort"
)

/*
Go语言数据结构 - 映射（Map）

本文件涵盖：
1. 映射的定义、初始化和使用
2. 映射的基本操作
3. 映射的遍历
4. 映射的零值和nil检查
5. 映射作为函数参数
6. 实用示例和最佳实践
*/

func main() {
	fmt.Println("=== Go语言映射（Map）学习 ===")
	
	// 1. 映射基础
	demonstrateMapBasics()
	
	// 2. 映射操作
	demonstrateMapOperations()
	
	// 3. 映射遍历
	demonstrateMapIteration()
	
	// 4. 映射的高级用法
	demonstrateAdvancedMapUsage()
	
	// 5. 实用示例
	demonstratePracticalExamples()
}

// 演示映射基础
func demonstrateMapBasics() {
	fmt.Println("\n--- 映射基础 ---")
	
	// 1. 映射声明
	var m1 map[string]int // 声明但未初始化，值为nil
	fmt.Printf("nil映射: %v (长度: %d, 是否为nil: %t)\n", m1, len(m1), m1 == nil)
	
	// 2. 使用make创建映射
	m2 := make(map[string]int)
	fmt.Printf("make创建的映射: %v (长度: %d, 是否为nil: %t)\n", m2, len(m2), m2 == nil)
	
	// 3. 映射字面量初始化
	m3 := map[string]int{
		"apple":  5,
		"banana": 3,
		"orange": 8,
	}
	fmt.Printf("字面量初始化: %v (长度: %d)\n", m3, len(m3))
	
	// 4. 空映射字面量
	m4 := map[string]int{}
	fmt.Printf("空映射字面量: %v (长度: %d)\n", m4, len(m4))
	
	// 5. 映射的键类型要求
	// 键类型必须是可比较的类型：基本类型、数组、结构体（字段都可比较）
	// 不能使用：切片、映射、函数作为键
	
	// 字符串键
	stringMap := map[string]string{
		"name": "张三",
		"city": "北京",
	}
	fmt.Printf("字符串键映射: %v\n", stringMap)
	
	// 整数键
	intMap := map[int]string{
		1: "一",
		2: "二",
		3: "三",
	}
	fmt.Printf("整数键映射: %v\n", intMap)
	
	// 结构体键
	type Point struct {
		X, Y int
	}
	
	pointMap := map[Point]string{
		{0, 0}: "原点",
		{1, 1}: "对角线点",
		{3, 4}: "任意点",
	}
	fmt.Printf("结构体键映射: %v\n", pointMap)
}

// 演示映射操作
func demonstrateMapOperations() {
	fmt.Println("\n--- 映射操作 ---")
	
	// 创建一个映射
	scores := map[string]int{
		"张三": 85,
		"李四": 92,
		"王五": 78,
	}
	
	fmt.Printf("初始映射: %v\n", scores)
	
	// 1. 访问元素
	score := scores["张三"]
	fmt.Printf("张三的分数: %d\n", score)
	
	// 2. 检查键是否存在
	score, exists := scores["赵六"]
	if exists {
		fmt.Printf("赵六的分数: %d\n", score)
	} else {
		fmt.Printf("赵六不在映射中，默认值: %d\n", score)
	}
	
	// 3. 添加或修改元素
	scores["赵六"] = 88 // 添加新元素
	scores["张三"] = 90 // 修改现有元素
	fmt.Printf("添加和修改后: %v\n", scores)
	
	// 4. 删除元素
	delete(scores, "王五")
	fmt.Printf("删除王五后: %v\n", scores)
	
	// 5. 删除不存在的键（安全操作）
	delete(scores, "不存在的人")
	fmt.Printf("删除不存在的键后: %v\n", scores)
	
	// 6. 清空映射
	// Go没有内置的清空映射方法，需要重新创建或逐个删除
	for key := range scores {
		delete(scores, key)
	}
	fmt.Printf("清空后的映射: %v (长度: %d)\n", scores, len(scores))
	
	// 7. 映射的零值访问
	var nilMap map[string]int
	// 从nil映射读取是安全的，返回零值
	value := nilMap["key"]
	fmt.Printf("从nil映射读取: %d\n", value)
	
	// 但是向nil映射写入会panic
	// nilMap["key"] = 1 // 这会导致panic
	
	// 8. 映射容量
	// 映射没有容量概念，但可以在make时指定初始大小提高性能
	largeMap := make(map[int]string, 1000)
	largeMap[1] = "one"
	fmt.Printf("大容量映射: %v (长度: %d)\n", largeMap, len(largeMap))
}

// 演示映射遍历
func demonstrateMapIteration() {
	fmt.Println("\n--- 映射遍历 ---")
	
	fruits := map[string]int{
		"apple":  5,
		"banana": 3,
		"orange": 8,
		"grape":  12,
	}
	
	// 1. 遍历键和值
	fmt.Println("遍历键和值:")
	for fruit, count := range fruits {
		fmt.Printf("  %s: %d\n", fruit, count)
	}
	
	// 2. 只遍历键
	fmt.Println("只遍历键:")
	for fruit := range fruits {
		fmt.Printf("  %s\n", fruit)
	}
	
	// 3. 只遍历值
	fmt.Println("只遍历值:")
	for _, count := range fruits {
		fmt.Printf("  %d\n", count)
	}
	
	// 4. 映射遍历顺序是随机的
	fmt.Println("多次遍历顺序可能不同:")
	for i := 0; i < 3; i++ {
		fmt.Printf("第%d次遍历: ", i+1)
		for fruit := range fruits {
			fmt.Printf("%s ", fruit)
		}
		fmt.Println()
	}
	
	// 5. 有序遍历映射
	fmt.Println("有序遍历映射:")
	keys := make([]string, 0, len(fruits))
	for key := range fruits {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	
	for _, key := range keys {
		fmt.Printf("  %s: %d\n", key, fruits[key])
	}
}

// 演示映射的高级用法
func demonstrateAdvancedMapUsage() {
	fmt.Println("\n--- 映射高级用法 ---")
	
	// 1. 嵌套映射
	students := map[string]map[string]int{
		"张三": {
			"数学": 85,
			"英语": 92,
			"物理": 78,
		},
		"李四": {
			"数学": 90,
			"英语": 88,
			"物理": 95,
		},
	}
	
	fmt.Println("嵌套映射（学生成绩）:")
	for student, subjects := range students {
		fmt.Printf("  %s:\n", student)
		for subject, score := range subjects {
			fmt.Printf("    %s: %d\n", subject, score)
		}
	}
	
	// 2. 映射作为集合使用
	set := make(map[string]bool)
	items := []string{"apple", "banana", "apple", "orange", "banana"}
	
	// 添加到集合
	for _, item := range items {
		set[item] = true
	}
	
	fmt.Printf("原切片: %v\n", items)
	fmt.Printf("集合（去重）: ")
	for item := range set {
		fmt.Printf("%s ", item)
	}
	fmt.Println()
	
	// 3. 映射作为计数器
	counter := make(map[string]int)
	words := []string{"hello", "world", "hello", "go", "world", "hello"}
	
	for _, word := range words {
		counter[word]++
	}
	
	fmt.Printf("单词计数: %v\n", counter)
	
	// 4. 映射的值为切片
	groups := make(map[string][]string)
	people := []struct {
		name string
		city string
	}{
		{"张三", "北京"},
		{"李四", "上海"},
		{"王五", "北京"},
		{"赵六", "深圳"},
		{"钱七", "上海"},
	}
	
	for _, person := range people {
		groups[person.city] = append(groups[person.city], person.name)
	}
	
	fmt.Println("按城市分组:")
	for city, names := range groups {
		fmt.Printf("  %s: %v\n", city, names)
	}
	
	// 5. 映射作为函数参数（引用传递）
	originalMap := map[string]int{"a": 1, "b": 2}
	fmt.Printf("修改前: %v\n", originalMap)
	modifyMap(originalMap)
	fmt.Printf("修改后: %v\n", originalMap)
}

// 修改映射（引用传递）
func modifyMap(m map[string]int) {
	m["c"] = 3
	m["a"] = 10
	fmt.Printf("函数内: %v\n", m)
}

// 演示实用示例
func demonstratePracticalExamples() {
	fmt.Println("\n--- 实用示例 ---")
	
	// 1. 实现LRU缓存的简化版本
	cache := NewSimpleCache(3)
	cache.Put("key1", "value1")
	cache.Put("key2", "value2")
	cache.Put("key3", "value3")
	
	fmt.Printf("缓存状态: %v\n", cache.data)
	
	if value, found := cache.Get("key1"); found {
		fmt.Printf("获取key1: %s\n", value)
	}
	
	cache.Put("key4", "value4") // 这会淘汰最久未使用的键
	fmt.Printf("添加key4后: %v\n", cache.data)
	
	// 2. 实现字符串相似度检查
	str1 := "hello world"
	str2 := "hello go"
	similarity := calculateSimilarity(str1, str2)
	fmt.Printf("'%s' 和 '%s' 的相似度: %.2f\n", str1, str2, similarity)
	
	// 3. 实现简单的索引
	documents := []string{
		"Go is a programming language",
		"Python is also a programming language",
		"Go is fast and efficient",
	}
	
	index := buildIndex(documents)
	fmt.Println("文档索引:")
	for word, docIds := range index {
		fmt.Printf("  '%s': 出现在文档 %v\n", word, docIds)
	}
	
	// 4. 映射合并
	map1 := map[string]int{"a": 1, "b": 2}
	map2 := map[string]int{"b": 3, "c": 4}
	merged := mergeMaps(map1, map2)
	fmt.Printf("映射1: %v\n", map1)
	fmt.Printf("映射2: %v\n", map2)
	fmt.Printf("合并后: %v\n", merged)
}

// 简单缓存实现
type SimpleCache struct {
	data     map[string]string
	capacity int
	order    []string
}

func NewSimpleCache(capacity int) *SimpleCache {
	return &SimpleCache{
		data:     make(map[string]string),
		capacity: capacity,
		order:    make([]string, 0),
	}
}

func (c *SimpleCache) Get(key string) (string, bool) {
	value, exists := c.data[key]
	if exists {
		// 移动到最前面
		c.moveToFront(key)
	}
	return value, exists
}

func (c *SimpleCache) Put(key, value string) {
	if _, exists := c.data[key]; exists {
		c.data[key] = value
		c.moveToFront(key)
		return
	}
	
	if len(c.data) >= c.capacity {
		// 删除最久未使用的
		oldest := c.order[0]
		delete(c.data, oldest)
		c.order = c.order[1:]
	}
	
	c.data[key] = value
	c.order = append(c.order, key)
}

func (c *SimpleCache) moveToFront(key string) {
	for i, k := range c.order {
		if k == key {
			c.order = append(c.order[:i], c.order[i+1:]...)
			break
		}
	}
	c.order = append(c.order, key)
}

// 计算字符串相似度（基于共同字符）
func calculateSimilarity(str1, str2 string) float64 {
	chars1 := make(map[rune]int)
	chars2 := make(map[rune]int)
	
	for _, char := range str1 {
		chars1[char]++
	}
	
	for _, char := range str2 {
		chars2[char]++
	}
	
	common := 0
	for char, count1 := range chars1 {
		if count2, exists := chars2[char]; exists {
			if count1 < count2 {
				common += count1
			} else {
				common += count2
			}
		}
	}
	
	total := len(str1) + len(str2)
	if total == 0 {
		return 1.0
	}
	
	return float64(2*common) / float64(total)
}

// 构建简单的倒排索引
func buildIndex(documents []string) map[string][]int {
	index := make(map[string][]int)
	
	for docId, doc := range documents {
		words := splitWords(doc)
		seen := make(map[string]bool)
		
		for _, word := range words {
			if !seen[word] {
				index[word] = append(index[word], docId)
				seen[word] = true
			}
		}
	}
	
	return index
}

// 简单的单词分割
func splitWords(text string) []string {
	var words []string
	var current string
	
	for _, char := range text {
		if char == ' ' || char == '\t' || char == '\n' {
			if current != "" {
				words = append(words, current)
				current = ""
			}
		} else {
			current += string(char)
		}
	}
	
	if current != "" {
		words = append(words, current)
	}
	
	return words
}

// 合并两个映射
func mergeMaps(map1, map2 map[string]int) map[string]int {
	result := make(map[string]int)
	
	// 复制第一个映射
	for key, value := range map1 {
		result[key] = value
	}
	
	// 合并第二个映射（如果键冲突，使用第二个映射的值）
	for key, value := range map2 {
		result[key] = value
	}
	
	return result
}

/*
运行此程序的命令：
go run data-structures/02_maps.go

学习要点：
1. 映射是引用类型，零值为nil，需要用make初始化或使用字面量
2. 映射的键类型必须是可比较的，值类型可以是任意类型
3. 映射遍历顺序是随机的，如需有序遍历需要先排序键
4. 使用"comma ok"惯用法检查键是否存在
5. 映射作为函数参数时是引用传递，修改会影响原映射
6. 映射常用于实现集合、计数器、索引等数据结构
7. 删除操作是安全的，删除不存在的键不会报错
*/
