package main

import (
	"fmt"
	"sort"
)

/*
Go语言数据结构 - 数组和切片

本文件涵盖：
1. 数组的定义、初始化和使用
2. 切片的定义、初始化和使用
3. 切片的底层原理
4. 切片的常用操作
5. 数组和切片的区别
6. 多维数组和切片
*/

func main() {
	fmt.Println("=== Go语言数组和切片学习 ===")
	
	// 1. 数组基础
	demonstrateArrays()
	
	// 2. 切片基础
	demonstrateSlices()
	
	// 3. 切片的高级操作
	demonstrateSliceOperations()
	
	// 4. 切片的底层原理
	demonstrateSliceInternals()
	
	// 5. 多维数组和切片
	demonstrateMultiDimensional()
	
	// 6. 实用示例
	demonstratePracticalExamples()
}

// 演示数组
func demonstrateArrays() {
	fmt.Println("\n--- 数组 ---")
	
	// 1. 数组声明和初始化
	var arr1 [5]int // 声明长度为5的整数数组，自动初始化为零值
	fmt.Printf("零值数组: %v\n", arr1)
	
	// 2. 数组字面量初始化
	arr2 := [5]int{1, 2, 3, 4, 5}
	fmt.Printf("字面量初始化: %v\n", arr2)
	
	// 3. 部分初始化
	arr3 := [5]int{1, 2} // 其余元素为零值
	fmt.Printf("部分初始化: %v\n", arr3)
	
	// 4. 指定索引初始化
	arr4 := [5]int{0: 10, 2: 20, 4: 30}
	fmt.Printf("指定索引初始化: %v\n", arr4)
	
	// 5. 自动推断长度
	arr5 := [...]int{1, 2, 3, 4, 5, 6}
	fmt.Printf("自动推断长度: %v (长度: %d)\n", arr5, len(arr5))
	
	// 6. 数组访问和修改
	arr2[0] = 100
	fmt.Printf("修改后的数组: %v\n", arr2)
	
	// 7. 数组遍历
	fmt.Println("数组遍历:")
	for i := 0; i < len(arr2); i++ {
		fmt.Printf("  arr2[%d] = %d\n", i, arr2[i])
	}
	
	// 8. 使用range遍历
	fmt.Println("使用range遍历:")
	for index, value := range arr2 {
		fmt.Printf("  索引%d: %d\n", index, value)
	}
	
	// 9. 数组比较
	arr6 := [5]int{100, 2, 3, 4, 5}
	arr7 := [5]int{100, 2, 3, 4, 5}
	fmt.Printf("数组相等: %t\n", arr6 == arr7)
	
	// 10. 数组作为函数参数（值传递）
	fmt.Printf("原数组: %v\n", arr2)
	modifyArray(arr2)
	fmt.Printf("函数调用后: %v (未改变，因为是值传递)\n", arr2)
	
	// 11. 数组指针
	modifyArrayByPointer(&arr2)
	fmt.Printf("通过指针修改后: %v\n", arr2)
}

// 修改数组（值传递，不会影响原数组）
func modifyArray(arr [5]int) {
	arr[0] = 999
	fmt.Printf("函数内数组: %v\n", arr)
}

// 通过指针修改数组
func modifyArrayByPointer(arr *[5]int) {
	arr[0] = 888
	fmt.Printf("函数内通过指针修改: %v\n", *arr)
}

// 演示切片
func demonstrateSlices() {
	fmt.Println("\n--- 切片 ---")
	
	// 1. 切片声明
	var slice1 []int // 声明切片，初始为nil
	fmt.Printf("nil切片: %v (长度: %d, 容量: %d, 是否为nil: %t)\n", 
		slice1, len(slice1), cap(slice1), slice1 == nil)
	
	// 2. 使用make创建切片
	slice2 := make([]int, 5)    // 长度为5，容量为5
	slice3 := make([]int, 3, 5) // 长度为3，容量为5
	fmt.Printf("make创建切片2: %v (长度: %d, 容量: %d)\n", slice2, len(slice2), cap(slice2))
	fmt.Printf("make创建切片3: %v (长度: %d, 容量: %d)\n", slice3, len(slice3), cap(slice3))
	
	// 3. 切片字面量
	slice4 := []int{1, 2, 3, 4, 5}
	fmt.Printf("字面量切片: %v (长度: %d, 容量: %d)\n", slice4, len(slice4), cap(slice4))
	
	// 4. 从数组创建切片
	arr := [6]int{1, 2, 3, 4, 5, 6}
	slice5 := arr[1:4] // 从索引1到3（不包括4）
	slice6 := arr[:3]  // 从开始到索引2
	slice7 := arr[2:]  // 从索引2到结束
	slice8 := arr[:]   // 整个数组
	
	fmt.Printf("原数组: %v\n", arr)
	fmt.Printf("arr[1:4]: %v\n", slice5)
	fmt.Printf("arr[:3]: %v\n", slice6)
	fmt.Printf("arr[2:]: %v\n", slice7)
	fmt.Printf("arr[:]: %v\n", slice8)
	
	// 5. 切片修改会影响底层数组
	slice5[0] = 100
	fmt.Printf("修改切片后的数组: %v\n", arr)
	fmt.Printf("修改后的切片: %v\n", slice5)
	
	// 6. append操作
	slice9 := []int{1, 2, 3}
	fmt.Printf("原切片: %v (长度: %d, 容量: %d)\n", slice9, len(slice9), cap(slice9))
	
	slice9 = append(slice9, 4)
	fmt.Printf("append(4): %v (长度: %d, 容量: %d)\n", slice9, len(slice9), cap(slice9))
	
	slice9 = append(slice9, 5, 6, 7)
	fmt.Printf("append(5,6,7): %v (长度: %d, 容量: %d)\n", slice9, len(slice9), cap(slice9))
	
	// 7. append另一个切片
	slice10 := []int{8, 9, 10}
	slice9 = append(slice9, slice10...)
	fmt.Printf("append切片: %v (长度: %d, 容量: %d)\n", slice9, len(slice9), cap(slice9))
}

// 演示切片操作
func demonstrateSliceOperations() {
	fmt.Println("\n--- 切片操作 ---")
	
	// 1. copy操作
	source := []int{1, 2, 3, 4, 5}
	dest := make([]int, 3)
	
	n := copy(dest, source)
	fmt.Printf("源切片: %v\n", source)
	fmt.Printf("目标切片: %v\n", dest)
	fmt.Printf("复制了 %d 个元素\n", n)
	
	// 2. 切片删除元素
	slice := []int{1, 2, 3, 4, 5}
	fmt.Printf("原切片: %v\n", slice)
	
	// 删除索引2的元素
	index := 2
	slice = append(slice[:index], slice[index+1:]...)
	fmt.Printf("删除索引2后: %v\n", slice)
	
	// 3. 切片插入元素
	slice = []int{1, 2, 4, 5}
	fmt.Printf("原切片: %v\n", slice)
	
	// 在索引2插入元素3
	index = 2
	value := 3
	slice = append(slice[:index], append([]int{value}, slice[index:]...)...)
	fmt.Printf("在索引2插入3: %v\n", slice)
	
	// 4. 切片反转
	slice = []int{1, 2, 3, 4, 5}
	fmt.Printf("原切片: %v\n", slice)
	reverseSlice(slice)
	fmt.Printf("反转后: %v\n", slice)
	
	// 5. 切片排序
	slice = []int{5, 2, 8, 1, 9, 3}
	fmt.Printf("排序前: %v\n", slice)
	sort.Ints(slice)
	fmt.Printf("排序后: %v\n", slice)
	
	// 6. 切片查找
	target := 8
	index = findInSlice(slice, target)
	if index != -1 {
		fmt.Printf("找到 %d 在索引 %d\n", target, index)
	} else {
		fmt.Printf("未找到 %d\n", target)
	}
}

// 反转切片
func reverseSlice(slice []int) {
	for i, j := 0, len(slice)-1; i < j; i, j = i+1, j-1 {
		slice[i], slice[j] = slice[j], slice[i]
	}
}

// 在切片中查找元素
func findInSlice(slice []int, target int) int {
	for i, v := range slice {
		if v == target {
			return i
		}
	}
	return -1
}

// 演示切片内部原理
func demonstrateSliceInternals() {
	fmt.Println("\n--- 切片内部原理 ---")
	
	// 1. 切片的底层数组共享
	arr := [6]int{1, 2, 3, 4, 5, 6}
	slice1 := arr[1:4]
	slice2 := arr[2:5]
	
	fmt.Printf("原数组: %v\n", arr)
	fmt.Printf("slice1 (arr[1:4]): %v\n", slice1)
	fmt.Printf("slice2 (arr[2:5]): %v\n", slice2)
	
	// 修改slice1会影响slice2，因为它们共享底层数组
	slice1[1] = 100
	fmt.Printf("修改slice1[1]后:\n")
	fmt.Printf("数组: %v\n", arr)
	fmt.Printf("slice1: %v\n", slice1)
	fmt.Printf("slice2: %v\n", slice2)
	
	// 2. 切片扩容
	slice3 := []int{1, 2}
	fmt.Printf("初始切片: %v (长度: %d, 容量: %d)\n", slice3, len(slice3), cap(slice3))
	
	for i := 0; i < 10; i++ {
		slice3 = append(slice3, i)
		fmt.Printf("append(%d): 长度=%d, 容量=%d\n", i, len(slice3), cap(slice3))
	}
	
	// 3. 切片的三个参数：slice[low:high:max]
	arr2 := [6]int{1, 2, 3, 4, 5, 6}
	slice4 := arr2[1:3:4] // 从索引1到2，容量限制为4-1=3
	fmt.Printf("arr2[1:3:4]: %v (长度: %d, 容量: %d)\n", slice4, len(slice4), cap(slice4))
}

// 演示多维数组和切片
func demonstrateMultiDimensional() {
	fmt.Println("\n--- 多维数组和切片 ---")
	
	// 1. 二维数组
	var matrix [3][4]int
	fmt.Printf("零值二维数组:\n")
	printMatrix(matrix[:])
	
	// 2. 二维数组初始化
	matrix2 := [3][4]int{
		{1, 2, 3, 4},
		{5, 6, 7, 8},
		{9, 10, 11, 12},
	}
	fmt.Printf("初始化的二维数组:\n")
	printMatrix(matrix2[:])
	
	// 3. 二维切片
	rows, cols := 3, 4
	matrix3 := make([][]int, rows)
	for i := range matrix3 {
		matrix3[i] = make([]int, cols)
	}
	
	// 填充数据
	for i := 0; i < rows; i++ {
		for j := 0; j < cols; j++ {
			matrix3[i][j] = i*cols + j + 1
		}
	}
	
	fmt.Printf("二维切片:\n")
	for _, row := range matrix3 {
		fmt.Printf("%v\n", row)
	}
	
	// 4. 不规则二维切片
	triangle := [][]int{
		{1},
		{2, 3},
		{4, 5, 6},
		{7, 8, 9, 10},
	}
	
	fmt.Printf("不规则二维切片（三角形）:\n")
	for i, row := range triangle {
		fmt.Printf("第%d行: %v\n", i, row)
	}
}

// 打印矩阵
func printMatrix(matrix [][]int) {
	for _, row := range matrix {
		for _, val := range row {
			fmt.Printf("%3d ", val)
		}
		fmt.Println()
	}
}

// 演示实用示例
func demonstratePracticalExamples() {
	fmt.Println("\n--- 实用示例 ---")
	
	// 1. 实现栈
	stack := NewStack()
	stack.Push(1)
	stack.Push(2)
	stack.Push(3)
	
	fmt.Printf("栈: %v\n", stack.items)
	fmt.Printf("弹出: %d\n", stack.Pop())
	fmt.Printf("栈顶: %d\n", stack.Peek())
	fmt.Printf("栈: %v\n", stack.items)
	
	// 2. 实现队列
	queue := NewQueue()
	queue.Enqueue(1)
	queue.Enqueue(2)
	queue.Enqueue(3)
	
	fmt.Printf("队列: %v\n", queue.items)
	fmt.Printf("出队: %d\n", queue.Dequeue())
	fmt.Printf("队首: %d\n", queue.Front())
	fmt.Printf("队列: %v\n", queue.items)
	
	// 3. 切片去重
	numbers := []int{1, 2, 2, 3, 3, 3, 4, 5, 5}
	unique := removeDuplicates(numbers)
	fmt.Printf("原切片: %v\n", numbers)
	fmt.Printf("去重后: %v\n", unique)
	
	// 4. 切片过滤
	numbers2 := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
	evens := filter(numbers2, func(n int) bool { return n%2 == 0 })
	fmt.Printf("原切片: %v\n", numbers2)
	fmt.Printf("偶数: %v\n", evens)
}

// 栈实现
type Stack struct {
	items []int
}

func NewStack() *Stack {
	return &Stack{items: make([]int, 0)}
}

func (s *Stack) Push(item int) {
	s.items = append(s.items, item)
}

func (s *Stack) Pop() int {
	if len(s.items) == 0 {
		panic("栈为空")
	}
	index := len(s.items) - 1
	item := s.items[index]
	s.items = s.items[:index]
	return item
}

func (s *Stack) Peek() int {
	if len(s.items) == 0 {
		panic("栈为空")
	}
	return s.items[len(s.items)-1]
}

// 队列实现
type Queue struct {
	items []int
}

func NewQueue() *Queue {
	return &Queue{items: make([]int, 0)}
}

func (q *Queue) Enqueue(item int) {
	q.items = append(q.items, item)
}

func (q *Queue) Dequeue() int {
	if len(q.items) == 0 {
		panic("队列为空")
	}
	item := q.items[0]
	q.items = q.items[1:]
	return item
}

func (q *Queue) Front() int {
	if len(q.items) == 0 {
		panic("队列为空")
	}
	return q.items[0]
}

// 去重函数
func removeDuplicates(slice []int) []int {
	seen := make(map[int]bool)
	var result []int
	
	for _, v := range slice {
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}
	
	return result
}

// 过滤函数
func filter(slice []int, predicate func(int) bool) []int {
	var result []int
	for _, v := range slice {
		if predicate(v) {
			result = append(result, v)
		}
	}
	return result
}

/*
运行此程序的命令：
go run data-structures/01_arrays_slices.go

学习要点：
1. 数组长度固定，是值类型；切片长度可变，是引用类型
2. 切片底层基于数组，多个切片可以共享同一个底层数组
3. append操作可能触发扩容，创建新的底层数组
4. 使用make创建切片时可以指定长度和容量
5. 切片的三参数语法slice[low:high:max]可以限制容量
6. 多维切片比多维数组更灵活，可以创建不规则结构
7. 切片常用于实现栈、队列等数据结构
*/
