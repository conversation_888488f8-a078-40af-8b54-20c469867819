#!/bin/bash

# Go语言学习项目 - Web展示应用启动脚本

echo "=== Go语言学习项目Web展示应用 ==="
echo ""

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "❌ 错误: 未找到Go语言环境"
    echo "请先安装Go语言 (https://golang.org/dl/)"
    exit 1
fi

# 显示Go版本
echo "✅ Go版本: $(go version)"
echo ""

# 检查是否在正确的目录
if [ ! -f "go.mod" ]; then
    echo "❌ 错误: 请在项目根目录中运行此脚本"
    exit 1
fi

# 下载依赖
echo "📦 正在下载依赖包..."
go mod tidy
if [ $? -ne 0 ]; then
    echo "❌ 依赖下载失败"
    exit 1
fi
echo "✅ 依赖下载完成"
echo ""

# 进入web-viewer目录
cd web-viewer

# 检查web-viewer目录是否存在
if [ ! -d "." ]; then
    echo "❌ 错误: web-viewer目录不存在"
    exit 1
fi

# 检查必要的文件
if [ ! -f "main.go" ]; then
    echo "❌ 错误: main.go文件不存在"
    exit 1
fi

if [ ! -d "templates" ]; then
    echo "❌ 错误: templates目录不存在"
    exit 1
fi

if [ ! -d "static" ]; then
    echo "❌ 错误: static目录不存在"
    exit 1
fi

echo "🚀 启动Web服务器..."
echo "📍 服务地址: http://localhost:8080"
echo "🔧 按 Ctrl+C 停止服务器"
echo ""
echo "正在启动..."

# 启动Web服务器
go run main.go
