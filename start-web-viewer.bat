@echo off
chcp 65001 >nul
title Go语言学习项目Web展示应用

echo === Go语言学习项目Web展示应用 ===
echo.

REM 检查Go是否安装
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Go语言环境
    echo 请先安装Go语言 ^(https://golang.org/dl/^)
    pause
    exit /b 1
)

REM 显示Go版本
echo ✅ Go版本:
go version
echo.

REM 检查是否在正确的目录
if not exist "go.mod" (
    echo ❌ 错误: 请在项目根目录中运行此脚本
    pause
    exit /b 1
)

REM 下载依赖
echo 📦 正在下载依赖包...
go mod tidy
if %errorlevel% neq 0 (
    echo ❌ 依赖下载失败
    pause
    exit /b 1
)
echo ✅ 依赖下载完成
echo.

REM 进入web-viewer目录
cd web-viewer

REM 检查web-viewer目录是否存在
if not exist "main.go" (
    echo ❌ 错误: main.go文件不存在
    pause
    exit /b 1
)

if not exist "templates" (
    echo ❌ 错误: templates目录不存在
    pause
    exit /b 1
)

if not exist "static" (
    echo ❌ 错误: static目录不存在
    pause
    exit /b 1
)

echo 🚀 启动Web服务器...
echo 📍 服务地址: http://localhost:8080
echo 🔧 按 Ctrl+C 停止服务器
echo.
echo 正在启动...
echo.

REM 启动Web服务器
go run main.go

pause
