package main

import (
	"fmt"
	"unsafe"
)

/*
Go语言基础 - 变量声明、数据类型、常量

本文件涵盖：
1. 变量声明的多种方式
2. Go语言的基本数据类型
3. 常量的定义和使用
4. 类型转换
5. 零值概念
*/

func main() {
	fmt.Println("=== Go语言变量、数据类型和常量学习 ===")
	
	// 1. 变量声明的多种方式
	demonstrateVariableDeclaration()
	
	// 2. 基本数据类型
	demonstrateBasicTypes()
	
	// 3. 常量
	demonstrateConstants()
	
	// 4. 类型转换
	demonstrateTypeConversion()
	
	// 5. 零值
	demonstrateZeroValues()
}

// 演示变量声明的多种方式
func demonstrateVariableDeclaration() {
	fmt.Println("\n--- 变量声明方式 ---")
	
	// 方式1: var 关键字声明，指定类型
	var name string
	name = "张三"
	fmt.Printf("方式1 - var声明: %s\n", name)
	
	// 方式2: var 关键字声明并初始化
	var age int = 25
	fmt.Printf("方式2 - var声明并初始化: %d\n", age)
	
	// 方式3: var 关键字声明，类型推断
	var city = "北京"
	fmt.Printf("方式3 - var类型推断: %s\n", city)
	
	// 方式4: 短变量声明（最常用）
	email := "<EMAIL>"
	fmt.Printf("方式4 - 短变量声明: %s\n", email)
	
	// 方式5: 多变量声明
	var (
		firstName = "李"
		lastName  = "四"
		fullName  = firstName + lastName
	)
	fmt.Printf("方式5 - 多变量声明: %s\n", fullName)
	
	// 方式6: 多变量同时赋值
	x, y := 10, 20
	fmt.Printf("方式6 - 多变量同时赋值: x=%d, y=%d\n", x, y)
	
	// 变量交换（Go语言特色）
	x, y = y, x
	fmt.Printf("变量交换后: x=%d, y=%d\n", x, y)
}

// 演示基本数据类型
func demonstrateBasicTypes() {
	fmt.Println("\n--- 基本数据类型 ---")
	
	// 布尔类型
	var isActive bool = true
	fmt.Printf("布尔类型: %t\n", isActive)
	
	// 整数类型
	var (
		int8Val   int8   = 127          // -128 到 127
		int16Val  int16  = 32767        // -32768 到 32767
		int32Val  int32  = 2147483647   // -2^31 到 2^31-1
		int64Val  int64  = 9223372036854775807 // -2^63 到 2^63-1
		intVal    int    = 42           // 平台相关，32位或64位
	)
	
	fmt.Printf("int8: %d (大小: %d字节)\n", int8Val, unsafe.Sizeof(int8Val))
	fmt.Printf("int16: %d (大小: %d字节)\n", int16Val, unsafe.Sizeof(int16Val))
	fmt.Printf("int32: %d (大小: %d字节)\n", int32Val, unsafe.Sizeof(int32Val))
	fmt.Printf("int64: %d (大小: %d字节)\n", int64Val, unsafe.Sizeof(int64Val))
	fmt.Printf("int: %d (大小: %d字节)\n", intVal, unsafe.Sizeof(intVal))
	
	// 无符号整数类型
	var (
		uint8Val  uint8  = 255         // 0 到 255
		uint16Val uint16 = 65535       // 0 到 65535
		uint32Val uint32 = 4294967295  // 0 到 2^32-1
		uint64Val uint64 = 18446744073709551615 // 0 到 2^64-1
		uintVal   uint   = 42          // 平台相关
	)
	
	fmt.Printf("uint8: %d\n", uint8Val)
	fmt.Printf("uint16: %d\n", uint16Val)
	fmt.Printf("uint32: %d\n", uint32Val)
	fmt.Printf("uint64: %d\n", uint64Val)
	fmt.Printf("uint: %d\n", uintVal)
	
	// 浮点数类型
	var (
		float32Val float32 = 3.14159
		float64Val float64 = 3.141592653589793
	)
	
	fmt.Printf("float32: %.5f (大小: %d字节)\n", float32Val, unsafe.Sizeof(float32Val))
	fmt.Printf("float64: %.15f (大小: %d字节)\n", float64Val, unsafe.Sizeof(float64Val))
	
	// 复数类型
	var (
		complex64Val  complex64  = 1 + 2i
		complex128Val complex128 = 3 + 4i
	)
	
	fmt.Printf("complex64: %v\n", complex64Val)
	fmt.Printf("complex128: %v\n", complex128Val)
	fmt.Printf("实部: %.1f, 虚部: %.1f\n", real(complex128Val), imag(complex128Val))
	
	// 字符串类型
	var message string = "Hello, 世界!"
	fmt.Printf("字符串: %s (长度: %d字节)\n", message, len(message))
	
	// 字节类型（uint8的别名）
	var byteVal byte = 'A'
	fmt.Printf("字节: %c (ASCII: %d)\n", byteVal, byteVal)
	
	// 符文类型（int32的别名，用于Unicode字符）
	var runeVal rune = '中'
	fmt.Printf("符文: %c (Unicode: %d)\n", runeVal, runeVal)
}

// 演示常量
func demonstrateConstants() {
	fmt.Println("\n--- 常量 ---")
	
	// 基本常量
	const pi = 3.14159
	const greeting = "你好"
	
	fmt.Printf("圆周率: %.5f\n", pi)
	fmt.Printf("问候语: %s\n", greeting)
	
	// 类型化常量
	const typedPi float64 = 3.14159
	fmt.Printf("类型化常量: %.5f\n", typedPi)
	
	// 常量组
	const (
		StatusOK       = 200
		StatusNotFound = 404
		StatusError    = 500
	)
	
	fmt.Printf("HTTP状态码: OK=%d, NotFound=%d, Error=%d\n", 
		StatusOK, StatusNotFound, StatusError)
	
	// iota 枚举器
	const (
		Sunday = iota    // 0
		Monday           // 1
		Tuesday          // 2
		Wednesday        // 3
		Thursday         // 4
		Friday           // 5
		Saturday         // 6
	)
	
	fmt.Printf("星期: 周日=%d, 周一=%d, 周六=%d\n", Sunday, Monday, Saturday)
	
	// iota 的高级用法
	const (
		_  = iota             // 跳过第一个值
		KB = 1 << (10 * iota) // 1024
		MB                    // 1048576
		GB                    // 1073741824
	)
	
	fmt.Printf("存储单位: KB=%d, MB=%d, GB=%d\n", KB, MB, GB)
}

// 演示类型转换
func demonstrateTypeConversion() {
	fmt.Println("\n--- 类型转换 ---")
	
	// 数值类型转换
	var i int = 42
	var f float64 = float64(i)
	var u uint = uint(f)
	
	fmt.Printf("int转float64: %d -> %.1f\n", i, f)
	fmt.Printf("float64转uint: %.1f -> %d\n", f, u)
	
	// 字符串和数值转换需要使用strconv包
	// 这里只演示基本的字符和数值转换
	var char byte = 65
	fmt.Printf("数值转字符: %d -> %c\n", char, char)
	
	// 注意：Go语言不支持隐式类型转换
	var a int32 = 10
	var b int64 = int64(a) // 必须显式转换
	fmt.Printf("int32转int64: %d -> %d\n", a, b)
	
	// 浮点数转整数会截断小数部分
	var floatNum float64 = 3.99
	var intNum int = int(floatNum)
	fmt.Printf("浮点数转整数: %.2f -> %d (小数部分被截断)\n", floatNum, intNum)
}

// 演示零值
func demonstrateZeroValues() {
	fmt.Println("\n--- 零值 ---")
	
	// Go语言中，声明但未初始化的变量会被赋予零值
	var (
		zeroInt     int
		zeroFloat   float64
		zeroBool    bool
		zeroString  string
		zeroPointer *int
	)
	
	fmt.Printf("int的零值: %d\n", zeroInt)
	fmt.Printf("float64的零值: %.1f\n", zeroFloat)
	fmt.Printf("bool的零值: %t\n", zeroBool)
	fmt.Printf("string的零值: '%s' (空字符串)\n", zeroString)
	fmt.Printf("指针的零值: %v (nil)\n", zeroPointer)
	
	// 零值的实用性
	fmt.Println("\n零值的实用性:")
	var counter int // 自动初始化为0，可以直接使用
	counter++
	fmt.Printf("计数器: %d\n", counter)
	
	var buffer string // 自动初始化为空字符串，可以直接拼接
	buffer += "Hello"
	buffer += " World"
	fmt.Printf("缓冲区: %s\n", buffer)
}

/*
运行此程序的命令：
go run basics/01_variables_types.go

学习要点：
1. Go语言有多种变量声明方式，短变量声明(:=)最常用
2. Go是强类型语言，类型转换必须显式进行
3. 常量在编译时确定，iota提供了强大的枚举功能
4. 零值机制让变量总是有一个合理的初始状态
5. 理解不同数据类型的取值范围和内存占用
*/
