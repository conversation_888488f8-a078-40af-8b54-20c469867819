package main

import (
	"fmt"
	"math"
	"strings"
)

/*
Go语言基础 - 函数定义、参数传递、返回值

本文件涵盖：
1. 函数的基本定义和调用
2. 参数传递（值传递、引用传递）
3. 多返回值
4. 命名返回值
5. 可变参数
6. 匿名函数和闭包
7. 函数作为参数和返回值
8. 递归函数
*/

func main() {
	fmt.Println("=== Go语言函数学习 ===")
	
	// 1. 基本函数调用
	demonstrateBasicFunctions()
	
	// 2. 参数传递
	demonstrateParameterPassing()
	
	// 3. 多返回值
	demonstrateMultipleReturns()
	
	// 4. 可变参数
	demonstrateVariadicFunctions()
	
	// 5. 匿名函数和闭包
	demonstrateAnonymousFunctions()
	
	// 6. 函数作为参数和返回值
	demonstrateHigherOrderFunctions()
	
	// 7. 递归函数
	demonstrateRecursion()
}

// 演示基本函数
func demonstrateBasicFunctions() {
	fmt.Println("\n--- 基本函数 ---")
	
	// 调用无参数无返回值的函数
	greet()
	
	// 调用有参数的函数
	greetPerson("张三")
	
	// 调用有返回值的函数
	result := add(10, 20)
	fmt.Printf("10 + 20 = %d\n", result)
	
	// 调用有多个参数和返回值的函数
	sum, product := calculate(5, 3)
	fmt.Printf("5 + 3 = %d, 5 × 3 = %d\n", sum, product)
}

// 无参数无返回值的函数
func greet() {
	fmt.Println("你好，世界！")
}

// 有参数的函数
func greetPerson(name string) {
	fmt.Printf("你好，%s！\n", name)
}

// 有返回值的函数
func add(a, b int) int {
	return a + b
}

// 多个参数和返回值的函数
func calculate(a, b int) (int, int) {
	return a + b, a * b
}

// 演示参数传递
func demonstrateParameterPassing() {
	fmt.Println("\n--- 参数传递 ---")
	
	// 值传递（基本类型）
	num := 10
	fmt.Printf("调用前: num = %d\n", num)
	modifyValue(num)
	fmt.Printf("调用后: num = %d (值没有改变)\n", num)
	
	// 引用传递（指针）
	fmt.Printf("调用前: num = %d\n", num)
	modifyPointer(&num)
	fmt.Printf("调用后: num = %d (值被改变)\n", num)
	
	// 切片传递（引用类型）
	slice := []int{1, 2, 3}
	fmt.Printf("调用前: slice = %v\n", slice)
	modifySlice(slice)
	fmt.Printf("调用后: slice = %v (内容被改变)\n", slice)
	
	// 映射传递（引用类型）
	m := map[string]int{"a": 1, "b": 2}
	fmt.Printf("调用前: map = %v\n", m)
	modifyMap(m)
	fmt.Printf("调用后: map = %v (内容被改变)\n", m)
}

// 值传递：不会修改原始值
func modifyValue(n int) {
	n = 100
	fmt.Printf("函数内: n = %d\n", n)
}

// 指针传递：会修改原始值
func modifyPointer(n *int) {
	*n = 100
	fmt.Printf("函数内: *n = %d\n", *n)
}

// 切片传递：会修改原始切片的内容
func modifySlice(s []int) {
	if len(s) > 0 {
		s[0] = 100
	}
	fmt.Printf("函数内: slice = %v\n", s)
}

// 映射传递：会修改原始映射的内容
func modifyMap(m map[string]int) {
	m["c"] = 3
	fmt.Printf("函数内: map = %v\n", m)
}

// 演示多返回值
func demonstrateMultipleReturns() {
	fmt.Println("\n--- 多返回值 ---")
	
	// 基本多返回值
	quotient, remainder := divide(17, 5)
	fmt.Printf("17 ÷ 5 = %d 余 %d\n", quotient, remainder)
	
	// 错误处理模式
	result, err := safeDivide(10, 0)
	if err != nil {
		fmt.Printf("错误: %v\n", err)
	} else {
		fmt.Printf("结果: %.2f\n", result)
	}
	
	result, err = safeDivide(10, 2)
	if err != nil {
		fmt.Printf("错误: %v\n", err)
	} else {
		fmt.Printf("结果: %.2f\n", result)
	}
	
	// 命名返回值
	area, perimeter := rectangleInfo(5, 3)
	fmt.Printf("矩形面积: %.2f, 周长: %.2f\n", area, perimeter)
	
	// 忽略某些返回值
	_, remainder2 := divide(20, 7)
	fmt.Printf("20 ÷ 7 的余数: %d\n", remainder2)
}

// 基本多返回值函数
func divide(a, b int) (int, int) {
	return a / b, a % b
}

// 错误处理模式的函数
func safeDivide(a, b float64) (float64, error) {
	if b == 0 {
		return 0, fmt.Errorf("除数不能为零")
	}
	return a / b, nil
}

// 命名返回值函数
func rectangleInfo(width, height float64) (area, perimeter float64) {
	area = width * height
	perimeter = 2 * (width + height)
	return // 裸返回，自动返回命名的返回值
}

// 演示可变参数
func demonstrateVariadicFunctions() {
	fmt.Println("\n--- 可变参数 ---")
	
	// 调用可变参数函数
	fmt.Printf("sum() = %d\n", sum())
	fmt.Printf("sum(1) = %d\n", sum(1))
	fmt.Printf("sum(1, 2, 3) = %d\n", sum(1, 2, 3))
	fmt.Printf("sum(1, 2, 3, 4, 5) = %d\n", sum(1, 2, 3, 4, 5))
	
	// 传递切片给可变参数函数
	numbers := []int{10, 20, 30, 40}
	fmt.Printf("sum(numbers...) = %d\n", sum(numbers...))
	
	// 格式化输出（Printf就是可变参数函数）
	formatAndPrint("姓名: %s, 年龄: %d, 分数: %.2f", "李四", 25, 88.5)
	
	// 混合参数类型
	printInfo("张三", 30, "北京", "上海", "深圳")
}

// 可变参数函数：计算整数和
func sum(numbers ...int) int {
	total := 0
	for _, num := range numbers {
		total += num
	}
	return total
}

// 自定义格式化函数
func formatAndPrint(format string, args ...interface{}) {
	result := fmt.Sprintf(format, args...)
	fmt.Printf("格式化结果: %s\n", result)
}

// 混合参数：固定参数 + 可变参数
func printInfo(name string, age int, cities ...string) {
	fmt.Printf("姓名: %s, 年龄: %d\n", name, age)
	if len(cities) > 0 {
		fmt.Printf("去过的城市: %s\n", strings.Join(cities, ", "))
	}
}

// 演示匿名函数和闭包
func demonstrateAnonymousFunctions() {
	fmt.Println("\n--- 匿名函数和闭包 ---")
	
	// 匿名函数直接调用
	func(message string) {
		fmt.Printf("匿名函数: %s\n", message)
	}("Hello, World!")
	
	// 将匿名函数赋值给变量
	square := func(x int) int {
		return x * x
	}
	fmt.Printf("5的平方: %d\n", square(5))
	
	// 闭包：函数可以访问外部变量
	counter := createCounter()
	fmt.Printf("计数器: %d\n", counter()) // 1
	fmt.Printf("计数器: %d\n", counter()) // 2
	fmt.Printf("计数器: %d\n", counter()) // 3
	
	// 多个闭包实例
	counter1 := createCounter()
	counter2 := createCounter()
	fmt.Printf("计数器1: %d\n", counter1()) // 1
	fmt.Printf("计数器2: %d\n", counter2()) // 1
	fmt.Printf("计数器1: %d\n", counter1()) // 2
	
	// 闭包捕获循环变量的陷阱和解决方案
	demonstrateClosureTrap()
}

// 创建计数器闭包
func createCounter() func() int {
	count := 0
	return func() int {
		count++
		return count
	}
}

// 演示闭包捕获循环变量的陷阱
func demonstrateClosureTrap() {
	fmt.Println("闭包陷阱演示:")
	
	// 错误的方式：所有闭包都会捕获最后的i值
	var funcs []func()
	for i := 0; i < 3; i++ {
		funcs = append(funcs, func() {
			fmt.Printf("错误方式 - i: %d\n", i) // 都会打印3
		})
	}
	
	fmt.Println("错误的闭包:")
	for _, f := range funcs {
		f()
	}
	
	// 正确的方式：通过参数传递或创建新变量
	var correctFuncs []func()
	for i := 0; i < 3; i++ {
		i := i // 创建新变量
		correctFuncs = append(correctFuncs, func() {
			fmt.Printf("正确方式 - i: %d\n", i)
		})
	}
	
	fmt.Println("正确的闭包:")
	for _, f := range correctFuncs {
		f()
	}
}

// 演示高阶函数
func demonstrateHigherOrderFunctions() {
	fmt.Println("\n--- 高阶函数 ---")
	
	// 函数作为参数
	numbers := []int{1, 2, 3, 4, 5}
	
	doubled := mapInts(numbers, func(x int) int { return x * 2 })
	fmt.Printf("原数组: %v\n", numbers)
	fmt.Printf("翻倍后: %v\n", doubled)
	
	squared := mapInts(numbers, func(x int) int { return x * x })
	fmt.Printf("平方后: %v\n", squared)
	
	// 过滤函数
	evens := filterInts(numbers, func(x int) bool { return x%2 == 0 })
	fmt.Printf("偶数: %v\n", evens)
	
	// 函数作为返回值
	addFive := createAdder(5)
	fmt.Printf("10 + 5 = %d\n", addFive(10))
	
	multiplyByThree := createMultiplier(3)
	fmt.Printf("7 × 3 = %d\n", multiplyByThree(7))
	
	// 函数组合
	addThenMultiply := compose(
		func(x int) int { return x + 10 },
		func(x int) int { return x * 2 },
	)
	fmt.Printf("(5 + 10) × 2 = %d\n", addThenMultiply(5))
}

// 映射函数：对切片中的每个元素应用函数
func mapInts(slice []int, fn func(int) int) []int {
	result := make([]int, len(slice))
	for i, v := range slice {
		result[i] = fn(v)
	}
	return result
}

// 过滤函数：根据条件过滤切片元素
func filterInts(slice []int, predicate func(int) bool) []int {
	var result []int
	for _, v := range slice {
		if predicate(v) {
			result = append(result, v)
		}
	}
	return result
}

// 创建加法器函数
func createAdder(n int) func(int) int {
	return func(x int) int {
		return x + n
	}
}

// 创建乘法器函数
func createMultiplier(n int) func(int) int {
	return func(x int) int {
		return x * n
	}
}

// 函数组合
func compose(f, g func(int) int) func(int) int {
	return func(x int) int {
		return g(f(x))
	}
}

// 演示递归函数
func demonstrateRecursion() {
	fmt.Println("\n--- 递归函数 ---")
	
	// 阶乘
	fmt.Printf("5! = %d\n", factorial(5))
	
	// 斐波那契数列
	fmt.Printf("斐波那契数列前10项: ")
	for i := 0; i < 10; i++ {
		fmt.Printf("%d ", fibonacci(i))
	}
	fmt.Println()
	
	// 汉诺塔
	fmt.Println("汉诺塔移动步骤（3个盘子）:")
	hanoi(3, "A", "C", "B")
	
	// 二分查找（递归版本）
	sortedArray := []int{1, 3, 5, 7, 9, 11, 13, 15}
	target := 7
	index := binarySearch(sortedArray, target, 0, len(sortedArray)-1)
	if index != -1 {
		fmt.Printf("在数组 %v 中找到 %d，索引为 %d\n", sortedArray, target, index)
	} else {
		fmt.Printf("在数组中未找到 %d\n", target)
	}
}

// 递归计算阶乘
func factorial(n int) int {
	if n <= 1 {
		return 1
	}
	return n * factorial(n-1)
}

// 递归计算斐波那契数
func fibonacci(n int) int {
	if n <= 1 {
		return n
	}
	return fibonacci(n-1) + fibonacci(n-2)
}

// 汉诺塔递归解法
func hanoi(n int, from, to, aux string) {
	if n == 1 {
		fmt.Printf("  移动盘子从 %s 到 %s\n", from, to)
		return
	}
	hanoi(n-1, from, aux, to)
	fmt.Printf("  移动盘子从 %s 到 %s\n", from, to)
	hanoi(n-1, aux, to, from)
}

// 递归二分查找
func binarySearch(arr []int, target, left, right int) int {
	if left > right {
		return -1
	}
	
	mid := left + (right-left)/2
	if arr[mid] == target {
		return mid
	} else if arr[mid] > target {
		return binarySearch(arr, target, left, mid-1)
	} else {
		return binarySearch(arr, target, mid+1, right)
	}
}

/*
运行此程序的命令：
go run basics/03_functions.go

学习要点：
1. Go函数支持多返回值，常用于错误处理
2. 参数传递：基本类型是值传递，引用类型传递引用
3. 可变参数使用...语法，类似于其他语言的可变参数
4. 闭包可以捕获外部变量，注意循环变量的陷阱
5. 函数是一等公民，可以作为参数和返回值
6. 递归函数要注意基础情况，避免无限递归
7. 命名返回值可以提高代码可读性，支持裸返回
*/
