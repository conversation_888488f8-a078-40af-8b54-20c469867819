package main

import (
	"fmt"
	"math/rand"
	"time"
)

/*
Go语言基础 - 控制流程

本文件涵盖：
1. if/else 条件语句
2. for 循环（Go语言唯一的循环结构）
3. switch 语句
4. defer 语句
5. 标签和跳转语句
*/

func main() {
	fmt.Println("=== Go语言控制流程学习 ===")
	
	// 1. if/else 条件语句
	demonstrateIfElse()
	
	// 2. for 循环
	demonstrateForLoops()
	
	// 3. switch 语句
	demonstrateSwitch()
	
	// 4. defer 语句
	demonstrateDefer()
	
	// 5. 标签和跳转
	demonstrateLabelsAndJumps()
}

// 演示 if/else 条件语句
func demonstrateIfElse() {
	fmt.Println("\n--- if/else 条件语句 ---")
	
	// 基本 if 语句
	age := 18
	if age >= 18 {
		fmt.Println("已成年")
	}
	
	// if-else 语句
	score := 85
	if score >= 90 {
		fmt.Println("优秀")
	} else if score >= 80 {
		fmt.Println("良好")
	} else if score >= 60 {
		fmt.Println("及格")
	} else {
		fmt.Println("不及格")
	}
	
	// if 语句的初始化（Go语言特色）
	if num := rand.Intn(100); num > 50 {
		fmt.Printf("随机数 %d 大于50\n", num)
	} else {
		fmt.Printf("随机数 %d 小于等于50\n", num)
	}
	// 注意：num 变量只在 if 语句块内有效
	
	// 实用示例：错误检查模式
	if result, err := divide(10, 2); err != nil {
		fmt.Printf("计算错误: %v\n", err)
	} else {
		fmt.Printf("10 ÷ 2 = %.2f\n", result)
	}
	
	// 布尔表达式组合
	temperature := 25
	humidity := 60
	if temperature > 20 && temperature < 30 && humidity < 70 {
		fmt.Println("天气舒适")
	}
	
	// 指针和nil检查
	var ptr *int
	if ptr == nil {
		fmt.Println("指针为nil")
	}
	
	value := 42
	ptr = &value
	if ptr != nil {
		fmt.Printf("指针指向的值: %d\n", *ptr)
	}
}

// 辅助函数：除法运算
func divide(a, b float64) (float64, error) {
	if b == 0 {
		return 0, fmt.Errorf("除数不能为零")
	}
	return a / b, nil
}

// 演示 for 循环
func demonstrateForLoops() {
	fmt.Println("\n--- for 循环 ---")
	
	// 1. 传统的三部分for循环
	fmt.Println("1. 传统for循环:")
	for i := 0; i < 5; i++ {
		fmt.Printf("  i = %d\n", i)
	}
	
	// 2. while风格的for循环
	fmt.Println("2. while风格for循环:")
	count := 0
	for count < 3 {
		fmt.Printf("  count = %d\n", count)
		count++
	}
	
	// 3. 无限循环
	fmt.Println("3. 无限循环（带break）:")
	counter := 0
	for {
		if counter >= 3 {
			break
		}
		fmt.Printf("  counter = %d\n", counter)
		counter++
	}
	
	// 4. continue 语句
	fmt.Println("4. continue语句（跳过偶数）:")
	for i := 0; i < 10; i++ {
		if i%2 == 0 {
			continue // 跳过偶数
		}
		fmt.Printf("  奇数: %d\n", i)
	}
	
	// 5. 遍历字符串
	fmt.Println("5. 遍历字符串:")
	text := "Hello,世界"
	for i, char := range text {
		fmt.Printf("  索引%d: %c (Unicode: %d)\n", i, char, char)
	}
	
	// 6. 遍历切片
	fmt.Println("6. 遍历切片:")
	fruits := []string{"苹果", "香蕉", "橙子"}
	for index, fruit := range fruits {
		fmt.Printf("  %d: %s\n", index, fruit)
	}
	
	// 7. 只要值，不要索引
	fmt.Println("7. 只遍历值:")
	for _, fruit := range fruits {
		fmt.Printf("  水果: %s\n", fruit)
	}
	
	// 8. 只要索引，不要值
	fmt.Println("8. 只遍历索引:")
	for index := range fruits {
		fmt.Printf("  索引: %d\n", index)
	}
	
	// 9. 遍历映射
	fmt.Println("9. 遍历映射:")
	scores := map[string]int{
		"张三": 85,
		"李四": 92,
		"王五": 78,
	}
	for name, score := range scores {
		fmt.Printf("  %s: %d分\n", name, score)
	}
	
	// 10. 嵌套循环
	fmt.Println("10. 嵌套循环（乘法表）:")
	for i := 1; i <= 3; i++ {
		for j := 1; j <= 3; j++ {
			fmt.Printf("  %d × %d = %d\n", i, j, i*j)
		}
	}
}

// 演示 switch 语句
func demonstrateSwitch() {
	fmt.Println("\n--- switch 语句 ---")
	
	// 1. 基本switch语句
	day := time.Now().Weekday()
	fmt.Printf("今天是: ")
	switch day {
	case time.Monday:
		fmt.Println("星期一")
	case time.Tuesday:
		fmt.Println("星期二")
	case time.Wednesday:
		fmt.Println("星期三")
	case time.Thursday:
		fmt.Println("星期四")
	case time.Friday:
		fmt.Println("星期五")
	case time.Saturday, time.Sunday:
		fmt.Println("周末")
	}
	
	// 2. switch语句的初始化
	switch hour := time.Now().Hour(); {
	case hour < 6:
		fmt.Println("凌晨")
	case hour < 12:
		fmt.Println("上午")
	case hour < 18:
		fmt.Println("下午")
	default:
		fmt.Println("晚上")
	}
	
	// 3. 无表达式的switch（相当于if-else链）
	score := 85
	switch {
	case score >= 90:
		fmt.Println("等级: A")
	case score >= 80:
		fmt.Println("等级: B")
	case score >= 70:
		fmt.Println("等级: C")
	case score >= 60:
		fmt.Println("等级: D")
	default:
		fmt.Println("等级: F")
	}
	
	// 4. fallthrough 关键字
	fmt.Println("fallthrough示例:")
	grade := 'B'
	switch grade {
	case 'A':
		fmt.Println("优秀")
		fallthrough
	case 'B':
		fmt.Println("良好")
		fallthrough
	case 'C':
		fmt.Println("及格")
	case 'D':
		fmt.Println("不及格")
	}
	
	// 5. 类型switch（用于接口）
	var value interface{} = 42
	switch v := value.(type) {
	case int:
		fmt.Printf("整数: %d\n", v)
	case string:
		fmt.Printf("字符串: %s\n", v)
	case bool:
		fmt.Printf("布尔值: %t\n", v)
	default:
		fmt.Printf("未知类型: %T\n", v)
	}
}

// 演示 defer 语句
func demonstrateDefer() {
	fmt.Println("\n--- defer 语句 ---")
	
	// defer 语句会在函数返回前执行
	defer fmt.Println("这是第一个defer语句")
	defer fmt.Println("这是第二个defer语句")
	defer fmt.Println("这是第三个defer语句")
	
	fmt.Println("正常执行的语句")
	
	// defer 的执行顺序是LIFO（后进先出）
	fmt.Println("defer执行顺序演示:")
	for i := 0; i < 3; i++ {
		defer fmt.Printf("  defer %d\n", i)
	}
	
	// defer 的实用场景：资源清理
	demonstrateDeferUsage()
}

// 演示defer的实用场景
func demonstrateDeferUsage() {
	fmt.Println("\ndefer实用场景:")
	
	// 模拟文件操作
	fmt.Println("模拟打开文件")
	defer fmt.Println("模拟关闭文件") // 确保文件被关闭
	
	// 模拟互斥锁
	fmt.Println("模拟获取锁")
	defer fmt.Println("模拟释放锁") // 确保锁被释放
	
	fmt.Println("执行业务逻辑")
	
	// defer 可以修改命名返回值
	result := deferWithNamedReturn()
	fmt.Printf("函数返回值: %d\n", result)
}

// defer 修改命名返回值的示例
func deferWithNamedReturn() (result int) {
	defer func() {
		result++ // 修改返回值
	}()
	return 41 // 原本返回41，但defer会将其修改为42
}

// 演示标签和跳转语句
func demonstrateLabelsAndJumps() {
	fmt.Println("\n--- 标签和跳转语句 ---")
	
	// 1. break 跳出循环
	fmt.Println("1. break跳出循环:")
	for i := 0; i < 10; i++ {
		if i == 3 {
			fmt.Printf("  在i=%d时跳出循环\n", i)
			break
		}
		fmt.Printf("  i = %d\n", i)
	}
	
	// 2. continue 跳过当前迭代
	fmt.Println("2. continue跳过迭代:")
	for i := 0; i < 5; i++ {
		if i == 2 {
			fmt.Printf("  跳过i=%d\n", i)
			continue
		}
		fmt.Printf("  i = %d\n", i)
	}
	
	// 3. 标签和goto（不推荐使用，但在某些情况下有用）
	fmt.Println("3. 标签和goto:")
	i := 0
Loop:
	if i < 3 {
		fmt.Printf("  i = %d\n", i)
		i++
		goto Loop
	}
	
	// 4. 带标签的break（跳出外层循环）
	fmt.Println("4. 带标签的break:")
Outer:
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			if i == 1 && j == 1 {
				fmt.Printf("  在i=%d,j=%d时跳出外层循环\n", i, j)
				break Outer
			}
			fmt.Printf("  i=%d, j=%d\n", i, j)
		}
	}
	
	// 5. 带标签的continue（继续外层循环）
	fmt.Println("5. 带标签的continue:")
OuterLoop:
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			if j == 1 {
				fmt.Printf("  在i=%d,j=%d时继续外层循环\n", i, j)
				continue OuterLoop
			}
			fmt.Printf("  i=%d, j=%d\n", i, j)
		}
	}
}

/*
运行此程序的命令：
go run basics/02_control_flow.go

学习要点：
1. Go语言只有for一种循环结构，但用法灵活
2. if语句可以包含初始化语句，变量作用域仅限于if块
3. switch语句不需要break，默认不会fall through
4. defer语句确保资源清理，执行顺序是LIFO
5. range关键字用于遍历数组、切片、映射、字符串等
6. 标签和跳转语句在复杂的嵌套循环中很有用
*/
